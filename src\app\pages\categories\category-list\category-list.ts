import {
  Component,
  inject,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { ColorUtils } from '@core/utils/colors';
import { log } from '@core/utils/logger';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import { IImage } from '@models/interfaces/image';
import { TranslateModule } from '@ngx-translate/core';
import { ITableRowAction } from '@shared/table/types/table.action';
import {
  ITableColumn,
  requestFilterOperatorType,
} from '@shared/table/types/table.column';
import { ITableQuery } from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';

import { <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { trackEvent } from '@aptabase/web';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { DateFormatPipe } from '@core/pipes/date.pipe';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { DestroyService } from '@core/services/utils/destroy';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { TagBooleanStatusComponent } from '@shared/tags/tag-boolean-status/tag-boolean-status';
import {
  NzAvatarComponent,
  NzAvatarGroupComponent,
} from 'ng-zorro-antd/avatar';
import { NzImageModule, NzImageService } from 'ng-zorro-antd/image';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-category-list',
  standalone: true,
  imports: [
    TableComponent,
    DateFormatPipe,
    NullValuePipe,
    NzTagComponent,
    NgTemplateOutlet,
    EllipsisDirective,
    NgStyle,
    NzImageModule,
    NzAvatarComponent,
    NzAvatarGroupComponent,
    NzToolTipModule,
    TranslateModule,
    TagBooleanStatusComponent,
  ],
  providers: [TableQueryService, DestroyService],
  templateUrl: './category-list.html',
  styleUrl: './category-list.less',
})
export class CategoryListComponent {
  // SERVICES
  private headerService = inject(HeaderService);
  private categoryService = inject(CategoriesService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);
  private imageService = inject(NzImageService);

  tplId = viewChild<TemplateRef<any>>('tplId');
  tplIsOnline = viewChild<TemplateRef<any>>('tplIsOnline');
  tplImage = viewChild<TemplateRef<any>>('tplImage');
  tplShowInHome = viewChild<TemplateRef<any>>('tplShowInHome');
  tplTimestamp = viewChild<TemplateRef<any>>('tplTimestamp');
  tplColor = viewChild<TemplateRef<any>>('tplColor');
  // Convert to signals
  protected loading = signal<boolean>(false);
  protected data = signal<any[]>([]);
  protected dataQuery = signal<any>(null);
  protected tableLayoutSettings = signal<ITableSetting>(<ITableSetting>{});
  protected tableQueryRequest = signal<ITableQuery | null>(null);
  protected ColorUtils = ColorUtils;
  protected refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  constructor() {
    trackEvent('category_list_page');
    const settings: ITableSetting = {
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset: 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    };
    this.tableLayoutSettings.set(settings);
    this.headerService.setCurrentSection(currentSectionType.categoryList);
  }

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest.set(query);
    this.loading.set(true);

    this.categoryService.search(query.meta, query.filter).subscribe((value) => {
      this.data.set(value.data);

      const currentSettings = this.tableLayoutSettings();
      currentSettings.pagination.total = value.meta!.page.total;
      currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
      currentSettings.pagination.pageSize = value.meta!.page.pageSize;
      this.tableLayoutSettings.set({ ...currentSettings });
      this.loading.set(false);
    });
  }

  ngAfterViewInit(): void {
    const currentSettings = this.tableLayoutSettings();
    currentSettings.listOfColumns.forEach(
      (col) =>
        (col.cellTemplate = col.cellTemplateName
          ? this[col.cellTemplateName]()
          : null),
    );
    this.tableLayoutSettings.set({ ...currentSettings });
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: ICategory) => {
          console.log('ROW', row);
          this.router.navigate([row._id], { relativeTo: this.route });
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: ICategory) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.categoryService.delete(row._id).subscribe({
                next: () => {
                  this.onQueryChange(this.tableQueryRequest()!);
                  this.messageService.addSuccessMessage(
                    'CATEGORYS.deleteSuccess',
                  );
                  log(`CATEGORY ID: ${row._id} - Eliminato`);
                },
              });
            },
            title: 'CATEGORIES.confirmDeleteTitle',
            subtitle: 'CATEGORIES.confirmDeleteSubtitle',
            // () => {
            //   this.messageService.addLoadingMessage();
            //   this.categoryService.delete(row._id).subscribe({
            //     next: () => {
            //       this.onQueryChange(this.tableQueryRequest);
            //       this.messageService.addSuccessMessage(
            //         'CATEGORYS.deleteSuccess',
            //       );
            //       log(`CATEGORY ID: ${row._id} - Eliminato`);
            //     },
            //   });
            // },
            // 'CATEGORIES.confirmDeleteTitle',
            // 'CATEGORIES.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: '_id',
        cellTemplate: this.tplId(),
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        lockOnLeft: true,
        visible: true,
        filterOperator: requestFilterOperatorType.mongoId,
        hasSearchFilter: true,
      },
      {
        id: 'images',
        cellTemplate: this.tplImage(),
        cellTemplateName: 'tplImage',
        title: 'CATEGORIES.image',
        width: '50px',
        visible: true,
      },
      {
        id: 'name',
        title: 'CATEGORIES.name',
        sortOrder: null,
        sortFn: true,
        width: '100px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'totalProducts',
        title: 'CATEGORIES.totalProducts',
        width: '60px',
        visible: true,
      },
      {
        id: 'showInHome',
        title: 'CATEGORIES.showInHome',
        cellTemplate: this.tplShowInHome(),
        cellTemplateName: 'tplShowInHome',
        sortOrder: null,
        sortFn: true,
        width: '60px',
        visible: true,
      },
      {
        id: 'color',
        title: 'CATEGORIES.color',
        cellTemplate: this.tplColor(),
        cellTemplateName: 'tplColor',
        sortOrder: null,
        sortFn: true,
        width: '60px',
        visible: true,
      },
    ];
  }

  goToDetail(categoryId: string) {
    this.router.navigate([categoryId], { relativeTo: this.route });
  }

  onImageClick(images: IImage[]): void {
    const imagePreview = images.map((image) => {
      return { src: image.url, alt: image.name };
    });
    this.imageService.preview(imagePreview, { nzZoom: 1, nzRotate: 0 });
  }
}
