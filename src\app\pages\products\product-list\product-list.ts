import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { httpResource } from '@angular/common/http';
import {
  ChangeDetectorRef,
  Component,
  inject,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { DateFormatPipe } from '@core/pipes/date.pipe';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { ProductsService } from '@core/services/http/products';
import { DestroyService } from '@core/services/utils/destroy';
import { HeaderService } from '@core/services/utils/header';
import { ModalService } from '@core/services/utils/modal';
import { ColorUtils } from '@core/utils/colors';
import { environment } from '@env/environment';
import { currentSectionType } from '@models/enums/current-section';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { IImage } from '@models/interfaces/image';
import { IProduct } from '@models/interfaces/product';
import { TranslateModule } from '@ngx-translate/core';
import { BadgeDiscountComponent } from '@shared/badge-discount/badge-discount';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { ITableRowAction } from '@shared/table/types/table.action';
import { ITableColumn } from '@shared/table/types/table.column';
import {
  IRequestFilter,
  IRequestMeta,
  ITableQuery,
} from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';
import { TagBooleanStatusComponent } from '@shared/tags/tag-boolean-status/tag-boolean-status';
import {
  NzAvatarComponent,
  NzAvatarGroupComponent,
} from 'ng-zorro-antd/avatar';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzTagComponent } from 'ng-zorro-antd/tag';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    NzButtonComponent,
    NzPopoverDirective,
    NzAvatarComponent,
    NzAvatarGroupComponent,
    NzTagComponent,
    NzTooltipDirective,
    NgStyle,
    TranslateModule,
    NullValuePipe,
    NgTemplateOutlet,
    CurrencyPipe,
    DateFormatPipe,
    EllipsisDirective,
    TagBooleanStatusComponent,
    BadgeDiscountComponent,
    TableComponent,
    NgTemplateOutlet,
  ],
  providers: [TableQueryService, DestroyService],
  templateUrl: './product-list.html',
  styleUrl: './product-list.less',
})
export class ProductListComponent {
  private cdr = inject(ChangeDetectorRef);

  // SERVICES
  private productService = inject(ProductsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);

  readonly tplId = viewChild<TemplateRef<any>>('tplId');
  readonly tplIsOnline = viewChild<TemplateRef<any>>('tplIsOnline');
  readonly tplName = viewChild<TemplateRef<any>>('tplName');
  readonly tplImage = viewChild<TemplateRef<any>>('tplImage');
  readonly tplTimestamp = viewChild<TemplateRef<any>>('tplTimestamp');
  readonly tplOptions = viewChild<TemplateRef<any>>('tplOptions');
  readonly tplColor = viewChild<TemplateRef<any>>('tplColor');
  protected loading: boolean = false;
  protected data: any[] = [];
  protected dataQuery: any;
  protected tableLayoutSettings: ITableSetting = <ITableSetting>{};
  protected tableQueryRequest: ITableQuery;
  protected ColorUtils = ColorUtils;
  // protected discounts = this.discountsService.discounts$;
  protected refreshData: IRefreshData = {
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  };
  constructor() {
    this.tableLayoutSettings.singleRowActions = this.buildRowActions();
    this.tableLayoutSettings.listOfColumns = this.buildTableColumns();
    this.tableLayoutSettings.dynamicTableHeightOffset = 375;
    this.tableLayoutSettings.pagination = {
      total: 0,
      pageSize: 50,
      pageIndex: 1,
    };

    // Initialize with empty data to ensure table renders properly
    this.data = [];
    this.headerService.setCurrentSection(currentSectionType.productList);
  }

  private _baseProductsApi = `${environment.api.products}`;

  findAll = (meta: IRequestMeta, filter: IRequestFilter[]) => {
    return httpResource<IBaseResponse<IProduct[], IFindResponseMeta>>(
      () => ({
        url: `${this._baseProductsApi}/search`,
        method: 'POST',
        body: {
          meta,
          filter,
        },
      }),
      {
        defaultValue: {
          data: [],
          meta: {
            page: {
              total: 0,
              pageIndex: 0,
              pageSize: 0,
            },
          },
        } as IBaseResponse<IProduct[], IFindResponseMeta>,
      },
    );
  };

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest = query;
    this.loading = true;

    this.productService.search(query.meta, query.filter).subscribe((value) => {
      this.data = value.data;

      this.tableLayoutSettings.pagination.total = value.meta.page.total;
      this.tableLayoutSettings.pagination.pageIndex = value.meta.page.pageIndex;
      this.tableLayoutSettings.pagination.pageSize = value.meta.page.pageSize;
      this.loading = false;
      this.cdr.detectChanges();
    });
    // this.loading = false;
    this.cdr.detectChanges();
  }

  ngAfterViewInit(): void {
    this.tableLayoutSettings.listOfColumns.forEach(
      (col) =>
        (col.cellTemplate = col.cellTemplateName
          ? this[col.cellTemplateName]
          : null),
    );
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'ACTIONS.modify',
        icon: 'edit',
        callbackFn: (row: IProduct) => {
          console.log('ROW', row);
          this.router.navigate([row._id], { relativeTo: this.route });
        },
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        callbackFn: (row: IProduct) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              // this.messageService.addLoadingMessage();
              // this.productService.delete(row._id).subscribe({
              //   next: () => {
              //     this.onQueryChange(this.tableQueryRequest);
              //     this.messageService.addSuccessMessage(
              //       'PRODUCT.deleteSuccess',
              //     );
              //     log(`PRODUCT ID: ${row._id} - Eliminato`);
              //   },
              // });
            },
            title: 'PRODUCTS.confirmDeleteTitle',
            subtitle: 'PRODUCTS.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: '_id',
        cellTemplate: this.tplId(),
        cellTemplateName: 'tplId',
        title: 'ID',
        sortOrder: null,
        sortFn: true,
        width: '60px',
        lockOnLeft: true,
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'images',
        title: 'PRODUCTS.images',
        cellTemplate: this.tplImage(),
        cellTemplateName: 'tplImage',
        width: '50px',
        visible: true,
      },
      {
        id: 'name',
        title: 'PRODUCTS.name',
        cellTemplate: this.tplName(),
        cellTemplateName: 'tplName',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'isOnline',
        title: 'PRODUCTS.isOnline',
        cellTemplate: this.tplIsOnline(),
        cellTemplateName: 'tplIsOnline',
        sortOrder: null,
        sortFn: true,
        width: '50px',
        visible: true,
        filterFn: true,
        listOfFilter: [
          {
            text: 'PRODUCTS.no',
            value: false,
          },
          {
            text: 'PRODUCTS.yes',
            value: true,
          },
        ],
      },
      {
        id: 'totalStockQuantity',
        title: 'PRODUCTS.totalQuantity',
        width: '60px',
        visible: true,
        sortFn: true,
      },
      {
        id: 'color',
        title: 'PRODUCTS.color',
        cellTemplate: this.tplColor(),
        cellTemplateName: 'tplColor',
        sortOrder: null,
        sortFn: true,
        width: '60px',
        visible: true,
      },
      {
        id: 'options',
        cellTemplate: this.tplOptions(),
        cellTemplateName: 'tplOptions',
        title: 'PRODUCTS.options',
        width: '80px',
        visible: true,
      },
    ];
  }

  goToDetail(productId: string) {
    this.router.navigate([productId], { relativeTo: this.route });
  }

  onImageClick(images: IImage[]): void {
    const imagePreview = images.map((image) => {
      return { src: image.url, alt: image.name };
    });
    // this.imageService.preview(imagePreview, { nzZoom: 1, nzRotate: 0 });
  }

  protected getFinalPrice(discountId: string, price: number) {
    // const percentage =
    //   this.discounts().find((item) => item._id == discountId)?.percentage || 0;

    // return percentage > 0 ? price * (1 - percentage / 100) : price;
    return 0;
  }
}
