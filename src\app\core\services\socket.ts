import { Injectable, signal } from '@angular/core';
import { GenericUtils } from '@core/utils/generic';
import { log } from '@core/utils/logger';
import { environment } from '@env/environment';
import {
  socketActionType,
  socketActionTypeResponse,
} from '@models/enums/socket';
import { Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';

/**
 * Servizio per la gestione della connessione socket.io lato client.
 * Permette di connettersi, disconnettersi e gestire eventi socket in tempo reale.
 */
@Injectable({
  providedIn: 'root',
})
export class SocketService {
  public socket: Socket;
  private _isSocketReady = signal<boolean>(false);
  public isSocketReady = this._isSocketReady.asReadonly();

  private _bookingsNeedUpdate = signal<number>(0);
  public bookingsNeedUpdate = this._bookingsNeedUpdate.asReadonly();

  private _dashboardNeedUpdate = signal<number>(0);
  public dashboardNeedUpdate = this._dashboardNeedUpdate.asReadonly();

  constructor() {}

  /**
   * Invia richiesta di connessione tramite socket.
   * @returns Observable che notifica la connessione
   */
  connect(): Observable<null> {
    return new Observable((observer) => {
      this.socket.emit(socketActionType.connection);
      observer.next(null);
    });
  }

  /**
   * Invia richiesta di uscita/disconnessione tramite socket.
   * @returns Observable che notifica la disconnessione
   */
  exit(): Observable<null> {
    log('exit socket...');
    return new Observable((observer) => {
      this.socket?.emit(socketActionType.exit);
      observer.next(null);
    });
  }

  /**
   * Disconnette e resetta la socket.
   * @returns void
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  /**
   * Inizializza la socket e registra tutti i listener degli eventi.
   * @returns void
   */
  initSocket(): void {
    // Check if socket is already initialized
    this.disconnect();

    this.socket = io(environment.api.base, {
      auth: {
        token: localStorage.getItem(GenericUtils.session_token),
      },
    });

    this.socket.on(socketActionTypeResponse.connected, () => {
      log('socket connected...');
      this._isSocketReady.set(true);
    });

    this.socket.on(socketActionTypeResponse.exited, () => {
      log('socket disconnected', this.socket);
      this.disconnect();
    });

    this.socket.on(socketActionTypeResponse.updatedBookings, () => {
      this._bookingsNeedUpdate.update((prev) => prev + 1);
    });

    this.socket.on(socketActionTypeResponse.updatedStaffSettings, () => {
      this._bookingsNeedUpdate.update((prev) => prev + 1);
    });

    this.socket.on(socketActionTypeResponse.updatdeDashboard, () => {
      this._dashboardNeedUpdate.update((prev) => prev + 1);
    });
  }
}
