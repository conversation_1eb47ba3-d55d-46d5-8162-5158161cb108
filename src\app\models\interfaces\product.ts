import { ICategory } from './category';
import { ICollection } from './collection';
import { IDiscount } from './discount';
import { IImage } from './image';

export interface IProduct {
  _id: string;
  name: string;
  variantName?: string;
  groupId: string;
  description: string;
  images: IImage[];
  categories: ICategory[];
  collectionGroup?: ICollection;
  isOnline: boolean;
  totalStockQuantity?: number;

  color?: IColor;

  options: IProductOption[];

  extraInfos?: IExtraInfo[];

  orderId?: string; //TODO
}

export interface IProductOption {
  _id: string;
  skuCode?: string;
  sizeType?: string;
  stockQuantity?: number;
  price?: number;
  discount?: IDiscount;
}

export interface IExtraInfo {
  key: string;
  value: string;
}

export interface IColor {
  hex: string;
  name: string;
}
