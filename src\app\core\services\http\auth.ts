import { Location } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { computed, inject, Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { GenericUtils } from '@core/utils/generic';
import { environment } from '@env/environment';
import { roleType } from '@models/enums/role';
import { IAdmin } from '@models/interfaces/admin';
import {
  IAuthResponse,
  IRegisterResponse,
} from '@models/interfaces/auth-response';
import { IBaseResponse } from '@models/interfaces/base-response';
import { catchError, switchMap, tap } from 'rxjs';
import { SocketService } from '../socket';

/**
 * Servizio che gestisce tutte le operazioni di autenticazione e gestione utente,
 * come login, registrazione, logout, gestione profilo, ruoli e sessione.
 * Fornisce metodi per interagire con l'API di autenticazione e amministrazione.
 */
@Injectable({ providedIn: 'root' })
export class AuthService {
  // SERVICES
  private http = inject(HttpClient);
  private router = inject(Router);
  private location = inject(Location);
  private socketService = inject(SocketService);

  // VARIABLES
  private _baseAuthApi = `${environment.api.auth}`;
  private _baseAdminApi = `${environment.api.admin}`;
  private _isLoggedIn = signal<boolean>(false);
  readonly isLoggedIn = this._isLoggedIn.asReadonly();
  private _user = signal<IAdmin | undefined>(undefined);
  public readonly user = this._user.asReadonly();

  // COMPUTED
  public readonly isAdmin = computed(
    () => this.user()?.role === roleType.admin,
  );

  public readonly userRole = computed(() => this.user()?.role);

  /**
   * Imposta l'utente autenticato nello stato locale.
   * @param user Oggetto utente autenticato
   */
  setLoggedUser(user: IAdmin) {
    this._user.set(user);
  }

  /**
   * Effettua il login dell'utente e inizializza la sessione.
   * @param email Email dell'utente
   * @param password Password dell'utente
   * @returns Observable con la risposta di autenticazione e profilo
   */
  login(email: string, password: string) {
    return this.http
      .post<IBaseResponse<IAuthResponse>>(`${this._baseAuthApi}/login`, {
        email,
        password,
      })
      .pipe(
        tap((res) => {
          localStorage.setItem(GenericUtils.session_token, res.data!.token);
          localStorage.setItem(GenericUtils.user_id, res.data!.id);
          localStorage.setItem(GenericUtils.tenantID, res.data!.tenantID);
          this.setLoggedIn(true);
          this.socketService.initSocket();
        }),
        switchMap((res) => this.getStaffProfile(res.data!.id)),
      );
  }

  /**
   * Registra un nuovo utente.
   * @param registerData Dati di registrazione (email, password, nome, cognome)
   * @returns Observable con la risposta di registrazione
   */
  register(registerData: {
    email: string;
    password: string;
    name: string;
    surname: string;
  }) {
    return this.http.post<IBaseResponse<IRegisterResponse>>(
      `${this._baseAuthApi}/register`,
      registerData,
    );
  }

  /**
   * Recupera il profilo dello staff tramite ID e aggiorna lo stato utente.
   * @param staffId ID dello staff
   * @returns Observable con il profilo utente
   */
  getStaffProfile(staffId: string) {
    return this.http
      .get<IBaseResponse<IAdmin>>(`${this._baseAdminApi}/${staffId}`)
      .pipe(tap((res) => this.setLoggedUser(res.data!)));
  }

  /**
   * Effettua il logout dell'utente, chiude la socket, pulisce lo storage e reindirizza alla login.
   * @returns Observable con la risposta di logout
   */
  logout() {
    return this.http
      .get<IBaseResponse<void>>(`${this._baseAuthApi}/logout`)
      .pipe(
        tap(() => {
          this.socketService.exit().subscribe();
          localStorage.clear();
          if (this.location.path() !== '/auth/register') {
            this.router.navigateByUrl('/auth/login').then(() => {
              this.setLoggedIn(false);
            });
          }
        }),
        catchError((err) => {
          this.socketService.exit().subscribe();
          localStorage.clear();
          if (this.location.path() !== '/auth/register') {
            this.router.navigateByUrl('/auth/login').then(() => {
              this.setLoggedIn(false);
            });
          }
          console.error(err.message);
          throw err;
        }),
      );
  }

  /**
   * Imposta lo stato di login dell'utente.
   * @param value true se loggato, false altrimenti
   */
  setLoggedIn(value: boolean) {
    this._isLoggedIn.set(value);
  }

  /**
   * Invia una richiesta di reset password all'email specificata.
   * @param email Email dell'utente
   * @returns Observable con la risposta
   */
  forgotPassword(email: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseAuthApi}/forgot-password`,
      { email },
    );
  }

  /**
   * Reimposta la password tramite token ricevuto via email.
   * @param password Nuova password
   * @param token Token di reset
   * @returns Observable con la risposta
   */
  resetPassword(password: string, token: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseAuthApi}/reset-password?token=${token}`,
      { password },
    );
  }

  /**
   * Attiva l'account tramite token ricevuto via email.
   * @param password Password scelta
   * @param token Token di attivazione
   * @returns Observable con la risposta
   */
  activateAccount(password: string, token: string) {
    return this.http.put<IBaseResponse<void>>(
      `${this._baseAuthApi}/activate-account?token=${token}`,
      { password },
    );
  }

  /**
   * Richiede un nuovo token di autenticazione.
   * @returns Observable con il nuovo token
   */
  refreshToken() {
    return this.http
      .get<
        IBaseResponse<{ token: string }>
      >(`${this._baseAuthApi}/refresh-token`)
      .pipe(
        tap((res) => {
          localStorage.setItem(GenericUtils.session_token, res.data!.token);
        }),
      );
  }

  /**
   * Invia nuovamente l'email di attivazione account.
   * @param staffId ID dello staff
   * @returns Observable con la risposta
   */
  resendActivationAccount(staffId: string) {
    return this.http.get<IBaseResponse<void>>(
      `${this._baseAuthApi}/${staffId}/resend-activation-account`,
    );
  }

  /**
   * Verifica se l'utente ha uno dei ruoli specificati.
   * @param roles Array di ruoli
   * @returns true se l'utente ha uno dei ruoli
   */
  userHasRole(roles: roleType[]) {
    return roles.includes(this.user()?.role!);
  }

  /**
   * Verifica se una email è disponibile per la registrazione.
   * @param email Email da controllare
   * @returns Observable con la disponibilità
   */
  isEmailAvailable(email: string) {
    return this.http.get<IBaseResponse<{ available: boolean }>>(
      `${this._baseAuthApi}/is-email-available?email=${email}`,
    );
  }

  /**
   * Verifica se la registrazione è attualmente aperta.
   * @returns Observable con la disponibilità
   */
  isRegisterOpen() {
    return this.http.get<IBaseResponse<{ available: boolean }>>(
      `${this._baseAuthApi}/is-register-open`,
    );
  }
}
