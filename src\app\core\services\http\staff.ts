import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IPartialStaff, IStaff } from '@models/interfaces/staff';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, of, switchMap, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class StaffService implements CrudApiOperations<IStaff, string> {
  // SERVICES
  private http = inject(HttpClient);

  // PROPERTIES
  private _baseStaffUrl: string = environment.api.admin;

  private _staff = signal<IStaff | undefined>(undefined);
  public readonly staff$ = this._staff.asReadonly();

  public setStaff(staff: IStaff) {
    this._staff.set(staff);
  }

  create(staff: IStaff) {
    return this.http.post<IBaseResponse<IStaff>>(
      `${this._baseStaffUrl}`,
      staff,
    );
  }

  getStaffList() {
    return this.http.get<IBaseResponse<IPartialStaff[]>>(
      `${this._baseStaffUrl}`,
    );
  }

  updateCustomer(staffId: string, staff: IStaff) {
    return this.http.put<IBaseResponse<IStaff>>(
      `${this._baseStaffUrl}/${staffId}`,
      staff,
    );
  }

  delete(staffId: string) {
    return this.http.delete<void>(`${this._baseStaffUrl}/${staffId}`);
  }

  readOne(staffId: string) {
    return this.http
      .get<IBaseResponse<IStaff>>(`${this._baseStaffUrl}/${staffId}`)
      .pipe(tap((value) => this.setStaff(value.data!)));
  }

  getStaffProfile(id: string) {
    return this.http
      .get<IBaseResponse<IStaff>>(`${this._baseStaffUrl}/${id}`)
      .pipe(
        tap((res) => {
          this.setStaff(res.data!);
        }),
      );
  }

  updateIsEnableStatus(staffId: string, readCustomer?: boolean) {
    return this.http
      .patch<
        IBaseResponse<IStaff>
      >(`${this._baseStaffUrl}/${staffId}/change-enable-status`, null)
      .pipe(switchMap(() => (readCustomer ? this.readOne(staffId) : of(true))));
  }

  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<IStaff[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<IStaff[], IFindResponseMeta>>(
      `${this._baseStaffUrl}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
