<div class="container">
  <form nz-form [formGroup]="baseForm" nzLayout="vertical">
    <nz-spin [nzSpinning]="loading()">
      <div nz-row class="w-100" [nzGutter]="16">
        <div nz-col [nzSpan]="12">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'name'"
            [label]="'CATEGORIES.categoryName' | translate"
            [placeholder]="'CATEGORIES.categoryNamePlaceholder' | translate"
          ></app-input-generic>
        </div>

        <div nz-col [nzSpan]="12">
          <app-input-checkbox
            [parentForm]="baseForm"
            [controlName]="'showInHome'"
            [label]="'CATEGORIES.categoryStatus' | translate"
            [optionList]="showInHomeList"
            [name]="'CATEGORIES.categoryStatusName' | translate"
          >
          </app-input-checkbox>
        </div>
      </div>

      <div nz-row [nzAlign]="'top'">
        <div nz-row class="w-100" [nzGutter]="16">
          <div nz-col [nzSpan]="12">
            <!-- IMAGES -->
            <nz-form-item [formGroup]="baseForm">
              <nz-form-control>
                <div class="clearfix">
                  <nz-form-label [nzRequired]="false">
                    <i
                      nz-icon
                      class="info-icon"
                      nz-popover
                      [nzPopoverTitle]="'limits.limit' | translate"
                      [nzPopoverContent]="tplImageInfo"
                      nzType="info-circle"
                    ></i>
                    <strong>{{ 'image' | translate }}</strong>
                    <ng-template #tplImageInfo>
                      <div class="info-image">
                        <strong>{{ 'limits.width' | translate }}: </strong>
                        <div class="min-max">
                          <span>min {{ Limits.width.min }}px</span>
                          <span>max {{ Limits.width.max }}px</span>
                        </div>
                        <strong>{{ 'limits.heigth' | translate }}: </strong>
                        <div class="min-max">
                          <span>min {{ Limits.height.min }}px</span>
                          <span>max {{ Limits.height.max }}px</span>
                        </div>
                      </div>
                    </ng-template>
                  </nz-form-label>
                  <nz-upload
                    [nzAction]="'http://localhost:4201/api/fakeImage'"
                    [nzCustomRequest]="uploadRequest"
                    [(nzFileList)]="FileList"
                    [nzShowButton]="FileList.length < 1"
                    [nzFileType]="
                      'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
                    "
                    [nzHeaders]="setMediaUploadHeaders"
                    [nzPreview]="handlePreview"
                    [nzRemove]="removeItem"
                    [nzShowUploadList]="showUploadList"
                    nzListType="picture-card"
                  >
                    <div>
                      <i nz-icon nzType="plus"></i>
                      <div style="margin-top: 8px">Carica</div>
                    </div>
                  </nz-upload>
                  <nz-modal
                    [nzVisible]="previewVisible"
                    [nzContent]="modalContent"
                    [nzFooter]="null"
                    (nzOnCancel)="previewVisible = false"
                  >
                    <ng-template #modalContent>
                      <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
                    </ng-template>
                  </nz-modal>
                </div>
              </nz-form-control> </nz-form-item
          ></div>
          <div nz-col [nzSpan]="12">
            <div nz-row class="w-100">
              <div style="margin-bottom: 1.3rem">
                <app-toggle-checkbox
                  [label]="'CATEGORIES.color' | translate"
                  [checked]="showColor()"
                  (onCheckboxChange)="onShowColorChange()"
                ></app-toggle-checkbox
              ></div>
            </div>

            @if(showColor){
            <div nz-row>
              <div nz-col [nzSpan]="2">
                <div [formGroup]="color">
                  <nz-color-picker
                    formControlName="hex"
                  ></nz-color-picker> </div
              ></div>

              <div nz-col [nzSpan]="10">
                <app-input-generic
                  [parentForm]="color"
                  [controlName]="'name'"
                  [placeholder]="'CATEGORIES.namePlaceholder' | translate"
                ></app-input-generic
              ></div> </div
            >}
          </div>
        </div>
      </div>
    </nz-spin>
  </form>

  <nz-divider></nz-divider>

  <div class="text-right">
    <nz-space>
      <button *nzSpaceItem nz-button (click)="onAbortClick()">
        {{ abortButtonTitle() | translate }}
      </button>
      <button
        *nzSpaceItem
        nz-button
        nzType="primary"
        (click)="onDataSaveClick()"
        [disabled]="!isValidForm() || loading()"
      >
        {{ saveButtonTitle() | translate }}
        @if(crudMode() === crudActionType.create) {
          <i nz-icon nzType="plus"></i>
        } @else {
          <i nz-icon nzType="edit"></i>
        }
      </button>
    </nz-space>
  </div>
</div>
