import { Routes } from '@angular/router';
import { authGuard } from '@core/guards/auth-guard';
import { roleType } from '@models/enums/role';
import { Dashboard } from '@pages/dashboard/dashboard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full',
  },
  {
    path: 'dashboard',
    component: Dashboard,
    canActivate: [authGuard],
    data: { preload: true, rolePermission: [roleType.admin, roleType.staff] },
  },
  {
    path: 'auth',
    loadChildren: () =>
      import('@pages/auth/auth.routes').then((m) => m.AUTH_ROUTES),
  },
  {
    path: 'products',
    loadChildren: () =>
      import('./pages/products/products.routes').then((m) => m.PRODUCTS_ROUTES),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'categories',
    loadChildren: () =>
      import('./pages/categories/categories.routes').then(
        (m) => m.CATEGORIES_ROUTES,
      ),
    data: { rolePermission: [roleType.admin, roleType.staff] },
    canActivate: [authGuard],
  },
  {
    path: 'staff',
    loadChildren: () =>
      import('./pages/staff/staff.routes').then((m) => m.STAFF_ROUTES),
    data: { rolePermission: [roleType.admin] },
    canActivate: [authGuard],
  },
];
