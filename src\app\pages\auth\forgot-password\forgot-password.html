<div class="container">
  <nz-spin [nzSpinning]="isLoading()">
    <a (click)="onBackToLoginClick()"
      ><span nz-icon nzType="arrow-left" nzTheme="outline" class="mr-4"></span
      >{{ "FORGOTPASSWORD.backToLogin" | translate }}</a
    >
    <h1 class="m-0">{{ "FORGOTPASSWORD.title" | translate }}</h1>
    <p class="mb-16">{{ "FORGOTPASSWORD.subtitle" | translate }}</p>
    <form
      nz-form
      [formGroup]="baseForm"
      class="forgot-form"
      [nzLayout]="'vertical'"
    >
      <app-input-generic
        [controlName]="'email'"
        [parentForm]="baseForm"
        [placeholder]="'********'"
        [prefixIcon]="'lock'"
        [label]="'FORGOTPASSWORD.email' | translate"
        [minLength]="5"
        [maxLength]="50"
      ></app-input-generic>
    </form>

    <app-simple-button
      [type]="'default'"
      [title]="'FORGOTPASSWORD.confirmButton' | translate | uppercase"
      (onButtonClick)="onForgotPasswordClick()"
      [disabled]="!baseForm.valid || isLoading()"
      [style]="{ width: '100%' }"
      [autoMinify]="false"
    >
    </app-simple-button>
    @if (forgotPassowrdError()) {
      <div class="error-message">
        <span nz-typography nzType="danger">{{
          "FORGOTPASSWORD.error" | translate
        }}</span>
      </div>
    }
  </nz-spin>
</div>
