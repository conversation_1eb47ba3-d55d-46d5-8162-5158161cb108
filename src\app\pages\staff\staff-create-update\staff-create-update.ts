import {
  Component,
  Signal,
  TemplateRef,
  afterNextRender,
  effect,
  inject,
  viewChild,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { AuthService } from '@core/services/http/auth';
import { StaffService } from '@core/services/http/staff';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { DestroyService } from '@core/services/utils/destroy';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { FormUtils } from '@core/utils/form';
import { CustomValidators } from '@core/validators/custom.validator';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { IStaff } from '@models/interfaces/staff';
import { TranslateModule } from '@ngx-translate/core';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { TagActiveStatusComponent } from '@shared/tags/tag-active-status/tag-active-status';
import { TagEnableStatusComponent } from '@shared/tags/tag-enable-status/tag-enable-status';
import { NzFormDirective } from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { switchMap, takeUntil, tap } from 'rxjs';
@Component({
  selector: 'app-staff-create-update',
  standalone: true,
  imports: [
    InputGenericComponent,
    FormsModule,
    NzFormDirective,
    TranslateModule,
    ReactiveFormsModule,
    NzRowDirective,
    NzColDirective,
    TagEnableStatusComponent,
    TagActiveStatusComponent,
    NzSpinComponent,
    SimpleButtonComponent,
  ],
  providers: [DestroyService],
  templateUrl: './staff-create-update.html',
  styleUrl: './staff-create-update.less',
})
export class StaffCreateUpdate extends CreateUpdateItem {
  // SERVICES
  private fb = inject(FormBuilder);
  private staffService = inject(StaffService);
  private messageService = inject(MessageService);
  private route = inject(ActivatedRoute);
  private destroy$ = inject(DestroyService);
  private router = inject(Router);
  private authService = inject(AuthService);
  private headerService = inject(HeaderService);
  private breadcrumbService = inject(BreadcrumbService);

  // PROPERTIES
  protected baseForm: FormGroup;
  protected crudMode: crudActionType = crudActionType.create;
  protected crudActionType = crudActionType;
  protected loading: boolean = true;
  protected staffId: string;
  protected buttonTitle: string = 'STAFF.saveStaff';
  protected staff: Signal<IStaff> = this.staffService.staff$;
  protected isAdmin = this.authService.isAdmin;

  // VIEWCHILD
  protected tplButton = viewChild<TemplateRef<any>>('tplButton');

  // EFFECTS
  private updateForm = effect(() => {
    this.staff();
    FormUtils.fillUpdateDataForm(this.baseForm, this.staff());
  });

  constructor() {
    super();
    trackEvent('staff_create_update_page');
    this.staffId = this.route.parent.snapshot.paramMap.get('id');

    if (!this.staffId) {
      this.staffId = this.authService.user()?.id;
    }

    this.initForm();
    this.route.paramMap
      .pipe(
        takeUntil(this.destroy$),
        switchMap((params) => {
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode = <crudActionType>data['crudType'];
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();

    afterNextRender(() => {
      this.headerService.setCurrentSection(
        currentSectionType.staffDetail,
        this.tplButton(),
      );
    });
  }

  setCrudMode() {
    switch (this.crudMode) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  setCreateMode() {
    this.buttonTitle = 'STAFF.saveStaff';
    this.loading = false;
  }

  setUpdateMode() {
    this.baseForm.get('email').disable();
    this.buttonTitle = 'STAFF.updateStaff';

    this.staffService
      .readOne(this.staffId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          FormUtils.fillUpdateDataForm(this.baseForm, res.data);
          this.loading = false;
        },
        error: () => {
          this.router.navigateByUrl('/staff');
        },
      });
  }

  private initForm() {
    this.baseForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      surname: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      email: [
        '',
        [
          Validators.required,
          Validators.minLength(5),
          Validators.maxLength(50),
          Validators.pattern(CustomValidators.emailRegex),
        ],
      ],
      isEnabled: [{ value: true, disabled: true }, [Validators.required]],
      isActive: [{ value: true, disabled: true }, [Validators.required]],
    });
  }

  onDataSubmit() {
    this.loading = true;
    const staff = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');
    this.staffService
      .create(staff)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.baseForm.reset();
          this.messageService.addSuccessMessage('STAFF.createSuccess');

          this.loading = false;
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  onDataUpdate() {
    this.loading = true;
    const staff = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');
    this.staffService
      .updateCustomer(this.staffId, staff)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          this.messageService.addSuccessMessage('STAFF.updateSuccess');
          const breadcrumbData = res.data?.name + ' ' + res.data?.surname;
          this.breadcrumbService.setBreadcrumbData(breadcrumbData);
          this.loading = false;
        },
        error: () => {
          this.loading = false;
        },
      });
  }

  onDataSaveClick() {
    switch (this.crudMode) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }
}
