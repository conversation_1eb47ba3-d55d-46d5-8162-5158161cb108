<div nz-row #target class="h-100">
  <div nz-col nzSpan="5" class="divider-right">
    <div class="left-panel">
      <div class="variants-container">
        @switch (true) {
          @case (customMode == customModeType.createVariant) {
            <ng-container *ngTemplateOutlet="tplCreate"></ng-container>
            <ng-container *ngTemplateOutlet="tplUpdate"></ng-container>
          }
          @case (crudMode == crudActionType.create) {
            <ng-container *ngTemplateOutlet="tplCreate"></ng-container>
          }
          @case (crudMode == crudActionType.update) {
            <ng-container *ngTemplateOutlet="tplUpdate"></ng-container>
            <button
              nz-button
              nzType="primary"
              class="w-100"
              (click)="onAddVariantClick()"
            >
              {{ 'PRODUCTS.addVariant' | translate }}
              <i nz-icon nzType="plus"></i>
            </button>
          }
        }
      </div>
    </div>
  </div>
  <div nz-col nzSpan="19" class="divider-center" class="h-100">
    <router-outlet></router-outlet>
  </div>
</div>

<ng-template #tplCreate>
  <nz-list class="sticky" nzItemLayout="horizontal" [nzLoading]="loading">
    <nz-list-item [ngClass]="{ 'border-bottom': linkedProducts()?.length > 0 }">
      <nz-list-item-meta class="active">
        @if (product()?.images?.length > 0) {
          <nz-list-item-meta-avatar
            [nzSrc]="product().images[0].url"
          ></nz-list-item-meta-avatar>
        } @else {
          <nz-list-item-meta-avatar
            [nzSrc]="'https://placehold.co/24x24'"
          ></nz-list-item-meta-avatar>
        }
        <nz-list-item-meta-title>{{
          customMode == customModeType.createVariant
            ? linkedProducts()?.[0]?.name
            : ('PRODUCTS.newProduct' | translate)
        }}</nz-list-item-meta-title>

        <nz-list-item-meta-description>
          {{ 'PRODUCTS.newVariant' | translate }}
        </nz-list-item-meta-description>
      </nz-list-item-meta>
    </nz-list-item>
  </nz-list>
</ng-template>

<ng-template #tplUpdate>
  <nz-list>
    @for (item of linkedProducts(); track $index) {
      <nz-list-item>
        <nz-list-item-meta [ngClass]="{ active: item._id == product()?._id }">
          @if (item?.images?.length > 0) {
            <nz-list-item-meta-avatar
              [nzSrc]="item.images[0].url"
            ></nz-list-item-meta-avatar>
          } @else {
            <nz-list-item-meta-avatar
              [nzSrc]="'https://placehold.co/24x24'"
            ></nz-list-item-meta-avatar>
          }

          <nz-list-item-meta-title>
            <a
              [ngClass]="{
                active: item._id == product()?._id
              }"
              (click)="onProductClick(item?._id)"
              >{{ item?.name }}</a
            >
          </nz-list-item-meta-title>

          <nz-list-item-meta-description>
            {{ item?.variantName | nullValue: 'noVariant' }}
          </nz-list-item-meta-description>
        </nz-list-item-meta>
      </nz-list-item>
    } @empty {
      <nz-list-empty></nz-list-empty>
    }
  </nz-list>
</ng-template>
