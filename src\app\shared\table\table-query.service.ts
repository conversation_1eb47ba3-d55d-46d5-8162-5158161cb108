import { Injectable, inject } from '@angular/core';

import { DestroyService } from '@core/services/utils/destroy';
import {
  LanguageService,
  languageCodeType,
} from '@core/services/utils/language';
import { CheckUtils } from '@core/utils/check';
import { NzTableQueryParams } from 'ng-zorro-antd/table';
import { Subject, takeUntil } from 'rxjs';
import { TableConfigService } from './table-config.service';
import { requestFilterOperatorType } from './types/table.column';
import { ITableFilterItem, tableFilterType } from './types/table.filter';
import {
  IRawTableFilterMetaData,
  IRequestFilter,
  ITableQuery,
  TableQuery,
} from './types/table.query';

@Injectable()
export class TableQueryService {
  private languageService = inject(LanguageService);
  private tableConfigService = inject(TableConfigService);

  private destroy$: DestroyService;
  private _rawTableFilterMetaData: IRawTableFilterMetaData = {
    queryParams: { pageIndex: 1, pageSize: 50, sort: null, filter: null },
    customFilters: [],
    requestFilters: [],
  };
  /**Language find request */
  private requestLanguage: languageCodeType;

  //Get Filter applied on table
  public readonly savedFilters$ = new Subject<IRawTableFilterMetaData>();
  /** if emit value, subscribe to the language service and build a new find request when language change */
  public readonly languageListner$ = new Subject<void>();
  // Find Object
  public readonly query$ = new Subject<ITableQuery>();

  constructor() {
    this.destroy$ = inject(DestroyService);
    this.languageListner$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.languageService.language$
        .pipe(takeUntil(this.destroy$))
        .subscribe(() => this.sendQueryObject());
    });
  }

  /**
   * Send Filter applied on table
   * */
  private sendSavedFilters() {
    this.savedFilters$.next(this._rawTableFilterMetaData);
  }

  /**
   * Delete single active filter
   * @param key
   */
  public deleteRawFilter(key: any) {
    this._rawTableFilterMetaData.customFilters =
      this._rawTableFilterMetaData.customFilters.filter(
        (item) => item.key !== key,
      );
    this._rawTableFilterMetaData.requestFilters = this.getNotNilFilters();
    if (this._rawTableFilterMetaData.requestFilters.length == 0)
      this.resetRawFilters();
    else this.tableConfigService.tableColumFilter$.next(key);
  }

  /**
   * Delete all Active Filters
   */
  public resetRawFilters() {
    this._rawTableFilterMetaData.queryParams.filter = [];
    this._rawTableFilterMetaData.customFilters = [];
    this._rawTableFilterMetaData.requestFilters = this.getNotNilFilters();
    this.tableConfigService.closeAllColumnFilters$.next();
  }

  /**
   * Update query Params
   * @param params
   */
  public updateQueryParams(params: NzTableQueryParams) {
    if (
      !CheckUtils.isEqualObjects(
        this._rawTableFilterMetaData.queryParams,
        params,
      )
    ) {
      this._rawTableFilterMetaData.queryParams = params;
      this.sendQueryObject();
    }
  }

  /**
   * Update Custom Filters
   * @param customFilters
   * @param isFindObjNotReady
   */
  public updateCustomFilters(
    customFilters: Array<ITableFilterItem>,
    isFindObjNotReady?: boolean,
  ) {
    this._rawTableFilterMetaData.customFilters = customFilters;
    if (!isFindObjNotReady) {
      this.resetPageIndex();
      this.sendQueryObject();
    }
  }

  /**
   * Reset Page Index
   */
  private resetPageIndex(params?: NzTableQueryParams) {
    if (params) params.pageIndex = 1;
    this._rawTableFilterMetaData.queryParams.pageIndex = 1;
  }

  /**
   * Create & Send Find Object
   */
  public sendQueryObject() {
    let newQuery = this.buildTableFilterMeta();
    this.sendSavedFilters();
    this.sendActiveFilters();
    this.query$.next(newQuery);
  }

  /**
   * Send active filters
   */
  private sendActiveFilters() {
    this.tableConfigService.activeFilter$.next(
      this._rawTableFilterMetaData.requestFilters,
    );
  }

  /**
   * Build Table Filter Meta
   * @returns RequestFind
   */
  private buildTableFilterMeta(): ITableQuery {
    this.requestLanguage = this.languageService.currentLanguage.code;
    const {
      pageSize,
      pageIndex = 1,
      sort,
      filter,
    } = this._rawTableFilterMetaData.queryParams;
    const newSort = sort.find((item) => item.value !== null);

    this._rawTableFilterMetaData.requestFilters = this.getNotNilFilters();

    let result: ITableQuery = new TableQuery(
      pageSize,
      pageIndex,
      [],
      this.requestLanguage,
    );

    if (newSort)
      result.meta.sort = {
        field: newSort.key,
        order: newSort.value,
      };
    if (this._rawTableFilterMetaData.requestFilters.length > 0) {
      result.filter = this.buildRequestFilter();
    }
    return result;
  }

  /**
   * Merge query Params & Custom Filters
   * @returns
   */
  private getNotNilFilters(): { key: string; value: any }[] {
    let result = [];
    result.push(
      ...this._rawTableFilterMetaData.customFilters.filter(
        (item) => !CheckUtils.isNullUndefinedOrEmpty(item.value),
      ),
    );
    return result;
  }

  /**
   * Build Request
   */
  private buildRequestFilter(): Array<IRequestFilter> {
    let result: Array<IRequestFilter> = [];
    this._rawTableFilterMetaData.requestFilters.forEach((f) =>
      result.push(this.buildQueryRequest(f)),
    );
    return result;
  }

  /**
   * Create find Obj with some operator
   * @param rawFilterItem
   * @returns
   */
  private buildQueryRequest(rawFilterItem: ITableFilterItem): IRequestFilter {
    let result: IRequestFilter = {
      operator: requestFilterOperatorType.or,
      items: [],
    };
    switch (rawFilterItem.filterType) {
      case tableFilterType.date:
        result.items.push({
          name: rawFilterItem.key,
          operator: <requestFilterOperatorType>rawFilterItem.filterOperator[0],
          type: rawFilterItem.filterTypeof,
          value: (<Date>rawFilterItem.value[0]).setMilliseconds(0),
        });
        result.items.push({
          name: rawFilterItem.key,
          operator: <requestFilterOperatorType>rawFilterItem.filterOperator[1],
          type: rawFilterItem.filterTypeof,
          value: (<Date>rawFilterItem.value[1]).setMilliseconds(999),
        });
        result.operator = requestFilterOperatorType.and;
        break;
      case tableFilterType.multiple:
        rawFilterItem.value.forEach((val: any) => {
          result.items.push({
            name: rawFilterItem.key,
            operator:
              val === requestFilterOperatorType.isNull
                ? requestFilterOperatorType.isNull
                : requestFilterOperatorType.eq,
            type: typeof val,
            value: val,
          });
        });
        break;
      case tableFilterType.search:
        result.items.push({
          name: rawFilterItem.key,
          operator:
            rawFilterItem.value === requestFilterOperatorType.isNull
              ? requestFilterOperatorType.isNull
              : <requestFilterOperatorType>rawFilterItem.filterOperator,
          type: rawFilterItem.filterTypeof,
          value: rawFilterItem.value,
        });
        break;
      case tableFilterType.singleDate:
        result.items.push({
          name: rawFilterItem.key,
          operator: <requestFilterOperatorType>rawFilterItem.filterOperator,
          type: rawFilterItem.filterTypeof,
          value: (<Date>rawFilterItem.value).setMilliseconds(
            this.getSingleDateMillisecondsByFilterOperator(
              <requestFilterOperatorType>rawFilterItem.filterTypeof,
            ),
          ),
        });
        break;
      case tableFilterType.radio:
      default:
        result.items.push({
          name: rawFilterItem.key,
          operator: <requestFilterOperatorType>rawFilterItem.filterOperator,
          type: rawFilterItem.filterTypeof,
          value: rawFilterItem.value,
        });
        break;
    }

    return result;
  }

  private getSingleDateMillisecondsByFilterOperator(
    filterOperator: requestFilterOperatorType,
  ) {
    switch (filterOperator) {
      case requestFilterOperatorType.ge:
      case requestFilterOperatorType.gt:
        return 0;
      case requestFilterOperatorType.le:
      case requestFilterOperatorType.lt:
        return 999;
      default:
        return 0;
    }
  }
}
