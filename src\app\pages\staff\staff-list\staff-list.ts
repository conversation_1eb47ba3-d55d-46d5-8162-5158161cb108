import {
  AfterViewInit,
  Component,
  DestroyRef,
  TemplateRef,
  inject,
  signal,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { trackEvent } from '@aptabase/web';
import { EllipsisDirective } from '@core/directives/ellipsis';
import { AuthService } from '@core/services/http/auth';
import { StaffService } from '@core/services/http/staff';
import { DestroyService } from '@core/services/utils/destroy';
import { HeaderService } from '@core/services/utils/header';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { log } from '@core/utils/logger';
import { currentSectionType } from '@models/enums/current-section';
import { roleType } from '@models/enums/role';
import { IStaff } from '@models/interfaces/staff';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TableComponent } from '@shared/table/table';
import { TableQueryService } from '@shared/table/table-query.service';
import { ITableRowAction } from '@shared/table/types/table.action';
import {
  ITableColumn,
  requestFilterOperatorType,
} from '@shared/table/types/table.column';
import { ITableQuery } from '@shared/table/types/table.query';
import { IRefreshData } from '@shared/table/types/table.refresh-data';
import { ITableSetting } from '@shared/table/types/table.settings';
import { TagActiveStatusComponent } from '@shared/tags/tag-active-status/tag-active-status';
import { TagEnableStatusComponent } from '@shared/tags/tag-enable-status/tag-enable-status';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import {
  NzTypographyComponent,
  NzTypographyModule,
} from 'ng-zorro-antd/typography';
@Component({
  selector: 'app-staff-list',
  standalone: true,
  imports: [
    TableComponent,
    EllipsisDirective,
    TagEnableStatusComponent,
    TagActiveStatusComponent,
    NzTooltipDirective,
    NzTypographyComponent,
    NzTypographyModule,
    NzTooltipDirective,
    TranslateModule,
  ],
  providers: [TableQueryService, DestroyService],
  templateUrl: './staff-list.html',
  styleUrl: './staff-list.less',
})
export class StaffList implements AfterViewInit {
  // SERVICES
  private StaffService = inject(StaffService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private modalService = inject(ModalService);
  private messageService = inject(MessageService);
  private headerService = inject(HeaderService);
  private authService = inject(AuthService);
  private translateService = inject(TranslateService);
  private destroyRef = inject(DestroyRef);

  // VIEWCHILD
  readonly tplId = viewChild<TemplateRef<any>>('tplId');
  readonly tplEllipsisText = viewChild<TemplateRef<any>>('tplEllipsisText');
  readonly tplEnableStatus = viewChild<TemplateRef<any>>('tplEnableStatus');
  readonly tplActiveStatus = viewChild<TemplateRef<any>>('tplActiveStatus');

  // PROPERTIES
  public loading = signal<boolean>(false);
  public data = signal<IStaff[]>([]);
  public tableLayoutSettings = signal<ITableSetting>({} as ITableSetting);
  public refreshData = signal<IRefreshData>({
    interval: 0,
    min: 0,
    max: 180,
    step: 30,
  });

  protected tableQueryRequest: ITableQuery;
  protected user = this.authService.user;

  // ENUMS
  roleType = roleType;

  constructor() {
    trackEvent('staff_list_page');

    const settings: ITableSetting = {
      singleRowActions: this.buildRowActions(),
      listOfColumns: this.buildTableColumns(),
      dynamicTableHeightOffset: 375,
      pagination: {
        total: 0,
        pageSize: 50,
        pageIndex: 1,
      },
    };

    this.tableLayoutSettings.set(settings);

    this.headerService.setCurrentSection(currentSectionType.staffList);
  }

  onQueryChange(query: ITableQuery) {
    this.tableQueryRequest = query;
    this.loading.set(true);
    this.StaffService.search(query.meta, query.filter)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        this.data.set(value.data);

        const currentSettings = this.tableLayoutSettings();
        currentSettings.pagination.total = value.meta!.page.total;
        currentSettings.pagination.pageIndex = value.meta!.page.pageIndex;
        currentSettings.pagination.pageSize = value.meta!.page.pageSize;
        this.tableLayoutSettings.set({ ...currentSettings });
        this.loading.set(false);
      });
  }

  ngAfterViewInit(): void {
    const currentSettings = this.tableLayoutSettings();
    currentSettings.listOfColumns.forEach(
      (col) =>
        (col.cellTemplate = col.cellTemplateName
          ? this[col.cellTemplateName]()
          : null),
    );
    this.tableLayoutSettings.set({ ...currentSettings });
  }

  goToDetail(StaffId: string) {
    this.router.navigate([StaffId], { relativeTo: this.route });
  }

  private buildTableColumns(): ITableColumn[] {
    return [
      {
        id: 'id',
        title: 'ID',
        cellTemplate: this.tplId(),
        cellTemplateName: 'tplId',
        filterOperator: requestFilterOperatorType.mongoId,
        sortOrder: null,
        sortFn: true,
        width: '60px',
        lockOnLeft: true,
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'email',
        title: 'STAFF.email',
        cellTemplate: this.tplEllipsisText(),
        cellTemplateName: 'tplEllipsisText',
        sortOrder: null,
        sortFn: true,
        width: '180px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'name',
        title: 'STAFF.name',
        cellTemplate: this.tplEllipsisText(),
        cellTemplateName: 'tplEllipsisText',
        width: '150px',
        visible: true,
        sortFn: true,
        hasSearchFilter: true,
      },
      {
        id: 'surname',
        title: 'STAFF.surname',
        cellTemplate: this.tplEllipsisText(),
        cellTemplateName: 'tplEllipsisText',
        sortOrder: null,
        sortFn: true,
        width: '150px',
        visible: true,
        hasSearchFilter: true,
      },
      {
        id: 'isEnabled',
        title: 'STAFF.isEnabled',
        sortOrder: null,
        cellTemplate: this.tplEnableStatus(),
        cellTemplateName: 'tplEnableStatus',
        sortFn: true,
        width: '120px',
        visible: true,
        filterFn: true,
        listOfFilter: [
          {
            value: true,
            text: this.translateService.instant('STAFF.enabled'),
          },
          {
            value: false,
            text: this.translateService.instant('STAFF.disabled'),
          },
        ],
      },
      {
        id: 'isActive',
        title: 'STAFF.isActive',
        cellTemplate: this.tplActiveStatus(),
        cellTemplateName: 'tplActiveStatus',
        sortOrder: null,
        sortFn: true,
        width: '120px',
        visible: true,
        filterFn: true,
        listOfFilter: [
          {
            value: true,
            text: this.translateService.instant('STAFF.active'),
          },
          {
            value: false,
            text: this.translateService.instant('STAFF.inactive'),
          },
        ],
      },
    ];
  }

  private buildRowActions(): ITableRowAction[] {
    return [
      {
        label: 'STAFF.modify',
        icon: 'edit',
        visibilityFn: (row: IStaff) => this.user().role === roleType.admin,
        callbackFn: (row: IStaff) => {
          this.router.navigate([row.id], { relativeTo: this.route });
        },
      },
      {
        label: 'STAFF.resendActivationAccount',
        icon: 'mail',
        callbackFn: (row: IStaff) => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.authService
                .resendActivationAccount(row.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.messageService.addSuccessMessage(
                    'STAFF.resendActivationSuccess',
                  );
                });
            },
            title: 'STAFF.activationAccountTitle',
            subtitle: 'STAFF.activationAccountSubtitle',
          });
        },
        visibilityFn: (row: IStaff) =>
          row.isEnabled &&
          !row.isActive &&
          row.role !== roleType.admin &&
          this.user().role === roleType.admin,
      },
      {
        label: 'ACTIONS.enable',
        icon: 'check-circle',
        callbackFn: (row: IStaff) => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.StaffService.updateIsEnableStatus(row.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.onQueryChange(this.tableQueryRequest);
                  this.messageService.addSuccessMessage('STAFF.updateSuccess');
                });
            },
            title: 'STAFF.enableTitle',
            subtitle: 'STAFF.enableSubtitle',
          });
        },
        visibilityFn: (row: IStaff) =>
          !row.isEnabled &&
          row.role !== roleType.admin &&
          row.isActive &&
          this.user().role === roleType.admin,
      },
      {
        label: 'ACTIONS.disable',
        icon: 'close-circle',
        callbackFn: (row: IStaff) => {
          this.modalService.infoModal({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.StaffService.updateIsEnableStatus(row.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(() => {
                  this.onQueryChange(this.tableQueryRequest);
                  this.messageService.addSuccessMessage('STAFF.updateSuccess');
                });
            },
            title: 'STAFF.disableTitle',
            subtitle: 'STAFF.disableSubtitle',
          });
        },
        visibilityFn: (row: IStaff) =>
          row.isEnabled &&
          row.role !== roleType.admin &&
          row.isActive &&
          this.user().role === roleType.admin,
      },
      {
        label: 'ACTIONS.delete',
        icon: 'delete',
        visibilityFn: (row: IStaff) =>
          row.role !== roleType.admin && this.user().role === roleType.admin,
        callbackFn: (row: IStaff) => {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.messageService.addLoadingMessage();
              this.StaffService.delete(row.id)
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe({
                  next: () => {
                    this.onQueryChange(this.tableQueryRequest);
                    this.messageService.addSuccessMessage(
                      'STAFF.deleteSuccess',
                    );
                    log(`StaffE ID: ${row.id} - Eliminato`);
                  },
                });
            },
            title: 'STAFF.confirmDeleteTitle',
            subtitle: 'STAFF.confirmDeleteSubtitle',
          });
        },
        danger: true,
      },
    ];
  }
}
