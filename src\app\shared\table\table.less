@import 'mixin';

.themeMixin({
  :host {

    .left {
      text-align: left;
    }

    .center {
      text-align: center;
    }

    .right {
      text-align: right;
    }

    nz-table {
      //  font-family:  Noto Sans SemCond ;
    }

    nz-card {
      //  font-family:  Noto Sans SemCond ;
    }

    th {
      // font-family:  Noto Sans SemCond SemBd ;
    }

    .ant-table-pagination {
      padding-right: 10px !important;
    }

    .row-action {
      width: 14px;
      height: 14px;
      padding: 0;
      font-size: 14px;
      border-radius: 2px;
      vertical-align: 0px;

      &.allowed {
        cursor: pointer;
      }

      &.not-allowed {
        cursor: context-menu;
      }
    }

    .ant-picker-footer-extra {
      padding: 0;
    }

    .font-20 {
      font-size: 20px;
    }

    .top-row {
      margin-bottom: 16px;
    }

    .filter-name-error {
      margin: 0;
      font-size: 11px;
    }

    .filter-box {
      padding: 8px;
    }

    .filter-box input {
      width: 188px;
      margin-bottom: 8px;
      display: block;
    }

    .filter-box button {
      width: 90px;
    }

    .search-button {
      margin-right: 8px;
    }

    .filters-pd-8 {
      padding: 8px;
    }

    .row-action {
      width: 14px;
      height: 14px;
      padding: 0;
      font-size: 14px;
      border-radius: 2px;
      vertical-align: 0px;

      &.allowed {
        cursor: pointer;
      }

      &.not-allowed {
        cursor: context-menu;
      }
    }

    .th-multiple-filter-border {
      border: none;
      border-bottom: 1px solid #f3f3f3;
    }
  }
});

// TABLE HEADER CUSTOM QUICK SEARCH
.filter-box {
  padding: 8px;
}

.filter-box button {
  width: 90px;
}