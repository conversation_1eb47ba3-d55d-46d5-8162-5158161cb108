import { Ng<PERSON>tyle } from '@angular/common';
import { Component, effect, inject, Signal, signal } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { DestroyService } from '@core/services/utils/destroy';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { TranslateModule } from '@ngx-translate/core';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { ToggleCheckboxComponent } from '@shared/toggles/toggle-checkbox/toggle-checkbox';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzColorPickerComponent } from 'ng-zorro-antd/color-picker';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormItemComponent,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzModalComponent } from 'ng-zorro-antd/modal';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import {
  NzUploadComponent,
  NzUploadFile,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, takeUntil, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-category-create-update',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzFormDirective,
    NzSpaceComponent,
    NzDividerComponent,
    NzButtonComponent,
    NzSpaceItemDirective,
    TranslateModule,
    InputGenericComponent,
    NzSpinComponent,
    NzRowDirective,
    NzColDirective,
    InputCheckboxComponent,
    ToggleCheckboxComponent,
    NzColorPickerComponent,
    NzFormControlComponent,
    NzFormItemComponent,
    NzFormLabelComponent,
    NzIconDirective,
    NzPopoverDirective,
    NzUploadComponent,
    NzModalComponent,
    NgStyle,
  ],
  providers: [UploadService, DestroyService],
  templateUrl: './category-create-update.html',
  styleUrl: './category-create-update.less',
})
export class CategoryCreateUpdateComponent extends CreateUpdateItem {
  private route = inject(ActivatedRoute);
  private destroy$ = inject(DestroyService);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private categoryService = inject(CategoriesService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);

  protected baseForm!: FormGroup;
  // Convert to signals
  protected showColor = signal<boolean>(false);
  protected saveButtonTitle = signal<string>('CATEGORIES.saveCategory');
  protected abortButtonTitle = signal<string>('CATEGORIES.deleteCategory');
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected categoryId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected category: Signal<ICategory | undefined> =
    this.categoryService.category$;

  private updateForm = effect(() => {
    if (!!this.category()) {
      this.fillFormData();
    }
  });

  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  protected showInHomeList = [
    { label: 'Mostra nella home', value: 'show', checked: true },
  ];

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  constructor() {
    super();
    this.initForm();
    this.headerService.setCurrentSection(currentSectionType.categoryDetail);
    this.route.paramMap
      .pipe(
        takeUntil(this.destroy$),
        switchMap((params) => {
          this.categoryId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();
  }
  get color() {
    return this.baseForm?.get('color') as FormGroup;
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };
  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  private initForm() {
    this.baseForm = this.fb.group({
      id: ['', [Validators.nullValidator]],
      name: ['', [Validators.required]],
      showInHome: [true, [Validators.nullValidator]],
      images: ['', [Validators.required, ValidateImages(1, 5)]],
      color: this.fb.group({
        hex: ['#1677ff', [Validators.required]],
        name: ['', [Validators.required]],
      }),
    });
  }

  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.color.disable();
    this.categoryService.setCategory(undefined);
    this.saveButtonTitle.set('CATEGORIES.saveCategory');
    this.abortButtonTitle.set('abort');
    this.loading.set(false);
  }

  override setUpdateMode() {
    this.saveButtonTitle.set('CATEGORIES.updateCategory');
    this.abortButtonTitle.set('CATEGORIES.deleteCategory');

    this.categoryService.readOne(this.categoryId()).subscribe({
      next: (res) => {
        this.fillFormData();
        this.initUploadService();
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/categories');
      },
    });
  }

  fillFormData() {
    this.baseForm.get('id')!.setValue(this.category()!._id);
    this.baseForm.get('name')!.setValue(this.category()!.name);
    this.baseForm.get('showInHome')!.setValue(this.category()!.showInHome);
    this.baseForm.get('images')!.setValue(this.category()!.images);
    console.log('BASE FORM', this.baseForm.value);

    if (!!this.category()!.color) {
      this.color.enable();
      this.showColor.set(true);
      this.color.get('name')!.setValue(this.category()!.color!.name);
      this.color.get('hex')!.setValue(this.category()!.color!.hex);
    } else {
      this.showColor.set(false);
      this.color.disable();
    }
  }

  onShowColorChange() {
    this.showColor.set(!this.showColor());
    if (this.showColor()) {
      this.color.enable();
    } else {
      this.color.disable();
    }
  }

  ngOnInit(): void {
    this.observeFormChanges();
  }

  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntil(this.destroy$))
      .subscribe((item) => {
        this.isValidForm.set(false);
      });

    this.baseForm.valueChanges.pipe(takeUntil(this.destroy$)).subscribe(() => {
      log('VALUE CHANGE', this.baseForm.value);
      this.isValidForm.set(this.baseForm.valid);
    });
  }

  onAbortClick() {
    // this.loading.set(true);
    switch (this.crudMode()) {
      case crudActionType.create:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.loading.set(false);
            this.router.navigateByUrl('/categories');
          },
          title: 'CATEGORIES.confirmDeleteTitle',
          subtitle: 'CATEGORIES.confirmDeleteSubtitle',
        });
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.categoryService.delete(this.categoryId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/categories');
                this.messageService.addSuccessMessage(
                  'CATEGORIES.deleteSuccess',
                );
                log(`CATEGORY ID: ${this.categoryId()} - Eliminato`);
              },
            });
          },
          title: 'CATEGORIES.confirmDeleteTitle',
          subtitle: 'CATEGORIES.confirmDeleteSubtitle',
        });
        break;
    }
  }

  onDataSaveClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  override onDataSubmit() {
    this.loading.set(true);
    const category = this.baseForm.value;
    if (!this.showColor()) delete category.color;
    FormUtils.removeObjectNullProperties(category);
    this.messageService.addLoadingMessage('loading');
    this.categoryService.create(category).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('CATEGORIES.createSuccess');
        this.loading.set(false);
        this.router.navigate(['categories', res.data!._id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  override onDataUpdate() {
    this.loading.set(true);
    const category = this.baseForm.value;
    if (!this.showColor()) delete category.color;
    this.messageService.addLoadingMessage('loading');
    console.log('CATEGORY', category);
    this.categoryService.update(this.categoryId(), category).subscribe({
      next: () => {
        this.messageService.addSuccessMessage('CATEGORIES.updateSuccess');
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }
}
