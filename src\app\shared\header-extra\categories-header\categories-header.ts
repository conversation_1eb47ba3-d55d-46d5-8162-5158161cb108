import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';

@Component({
  selector: 'app-categories-header',
  standalone: true,
  imports: [NzSpaceComponent, SimpleButtonComponent, NzSpaceItemDirective],
  templateUrl: './categories-header.html',
  styleUrl: './categories-header.less',
})
export class CategoriesHeader {
  private router = inject(Router);

  onAddCategoryClick() {
    this.router.navigateByUrl('/categories/create');
  }
}
