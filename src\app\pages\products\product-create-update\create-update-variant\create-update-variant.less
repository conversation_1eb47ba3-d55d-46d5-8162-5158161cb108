@import 'mixin';

.themeMixin({
    :host {

        .container {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 86%;

            .form {
                overflow-x: hidden;
                padding-left: 1.5rem;
                padding-right: 1rem;
                margin-right: -0.5rem;

                padding-bottom: 22px;
                margin-bottom: -22px;



                .final-price {
                    font-weight: bold;
                    font-size: 1.8rem;
                    padding-left: 8px;
                }
            }

            .collapse-header {
                display: grid;
                grid-template-columns: auto 84px 50px;
                width: 100%;
                font-weight: 600;
                column-gap: 12px;
                padding-right: 14px;

                // & :nth-child(2) {
                //   justify-self: center;
                // }

                & :nth-child(3) {
                    justify-self: flex-end
                }

                .option {
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }

                .price, .quantity {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    width: 100%;

                    .icon {
                        margin-top: -0.3rem;
                        margin-left: 0.7rem
                    }

                }


                .option-name {
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                span[nz-icon] {
                    color: #57758f;
                }
            }


        }

        .info-icon {
            margin-right: 6px;
        }

        .info-image {
            display: grid;
            grid-template-columns: 50% 50%;
            width: 100%;

            .min-max {
                display: flex;
            }
        }

        .primary-color {
            color: @primary-color;
        }

        .primary-color-border {
            border-top-color: @primary-color;
        }


    }


});