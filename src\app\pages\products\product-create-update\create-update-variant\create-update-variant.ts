import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON>tyle } from '@angular/common';
import { Component, effect, inject, signal, Signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CategoriesService } from '@core/services/http/categories';
import { ProductsService } from '@core/services/http/products';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { log } from '@core/utils/logger';
import { CustomValidators } from '@core/validators/custom.validator';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType, customModeType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { ICategory } from '@models/interfaces/category';
import { IDiscount } from '@models/interfaces/discount';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { IProduct } from '@models/interfaces/product';
import { TranslateModule } from '@ngx-translate/core';
import { BadgeDiscountComponent } from '@shared/badge-discount/badge-discount';
import { SimpleButtonComponent } from '@shared/buttons/simple-button/simple-button';
import { InputCheckboxComponent } from '@shared/inputs/input-checkbox/input-checkbox';
import { InputGenericComponent } from '@shared/inputs/input-generic/input-generic';
import { InputNumberComponent } from '@shared/inputs/input-number/input-number';
import { InputSelectComponent } from '@shared/inputs/input-select/input-select';
import { InputTextareaComponent } from '@shared/inputs/input-textarea/input-textarea';
import { ToggleCheckboxComponent } from '@shared/toggles/toggle-checkbox/toggle-checkbox';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import {
  NzCollapseComponent,
  NzCollapsePanelComponent,
} from 'ng-zorro-antd/collapse';
import { NzColorPickerComponent } from 'ng-zorro-antd/color-picker';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import {
  NzFormControlComponent,
  NzFormDirective,
  NzFormLabelComponent,
} from 'ng-zorro-antd/form';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzPopconfirmDirective } from 'ng-zorro-antd/popconfirm';
import { NzPopoverDirective } from 'ng-zorro-antd/popover';
import { NzSpaceComponent, NzSpaceItemDirective } from 'ng-zorro-antd/space';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import {
  NzUploadFile,
  NzUploadModule,
  NzUploadXHRArgs,
} from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};

@Component({
  selector: 'app-create-update-variant',
  standalone: true,
  imports: [
    InputCheckboxComponent,
    NzFormDirective,
    FormsModule,
    ReactiveFormsModule,
    InputTextareaComponent,
    InputGenericComponent,
    InputSelectComponent,
    NzUploadModule,
    InputNumberComponent,
    NzSpaceComponent,
    NzInputNumberModule,
    NgStyle,
    NzModalModule,
    TranslateModule,
    NzButtonComponent,
    NzIconDirective,
    NzSpaceComponent,
    NzFormControlComponent,
    NzPopoverDirective,
    NzFormLabelComponent,
    NzRowDirective,
    NzColDirective,
    NzDividerComponent,
    CurrencyPipe,
    NzColorPickerComponent,
    SimpleButtonComponent,
    NzCollapseComponent,
    NzCollapsePanelComponent,
    NzPopconfirmDirective,
    ToggleCheckboxComponent,
    NzSpaceItemDirective,
    BadgeDiscountComponent,
    NzSpinComponent,
    NzTooltipDirective,
  ],
  providers: [UploadService],
  templateUrl: './create-update-variant.html',
  styleUrl: './create-update-variant.less',
})
export class CreateUpdateVariantComponent extends CreateUpdateItem {
  // SERVICES
  private productService = inject(ProductsService);
  private headerService = inject(HeaderService);
  private route = inject(ActivatedRoute);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private categoriesService = inject(CategoriesService);

  protected baseForm!: FormGroup;
  protected crudMode: crudActionType = crudActionType.create;
  protected crudActionType = crudActionType;
  protected customMode: customModeType | undefined = undefined;
  protected productId!: string;
  protected loading: boolean = true;
  protected isValidForm: boolean = false;
  protected finalPrice: number = 0;
  protected showExtraInfo: boolean = false;
  protected showVariantName: boolean = false;
  protected showColor: boolean = false;
  protected saveButtonTitle: string = 'PRODUCTS.saveProduct';
  protected abortButtonTitle: string = 'PRODUCTS.deleteProduct';
  protected product: Signal<IProduct | undefined> =
    this.productService.product$;
  protected linkedProducts: Signal<Partial<IProduct[]> | undefined> =
    this.productService.linkedProducts$;

  private updateForm = effect(() => {
    if (!!this.product() && !this.customMode) {
      console.log('UPDATE EFFECT');
      this.fillFormData();
    }
  });

  protected showProductStatus = [
    { label: 'PRODUCTS.showOnShop', value: 'online', checked: true },
  ];

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  protected categoriesList = this.categoriesService.categories$;

  private discounts = signal<IDiscount[] | undefined>(undefined);

  constructor() {
    super();
    this.headerService.setCurrentSection(currentSectionType.productDetail);
    this.initForm();
    this.observeFormChanges();

    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.productId = <string>params.get('id');
          return this.route.data.pipe(
            tap((data) => {
              console.log('CREATE UPDATE VARIANT DATA', data);
              this.crudMode = <crudActionType>data['crudType'];
              this.customMode = <customModeType>data['customMode'];
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();
  }
  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  initForm() {
    this.baseForm = this.fb.group({
      _id: [null, [Validators.nullValidator]],
      groupId: [null, [Validators.nullValidator]],
      name: ['', [Validators.required]],
      variantName: [
        'UNICA',
        [
          Validators.required,
          CustomValidators.variantNameValidator(
            this.customMode,
            this.linkedProducts,
            this.product,
          ),
        ],
      ],
      description: ['', [Validators.required]],
      collectionGroup: [null, [Validators.nullValidator]],
      categories: [[], [Validators.nullValidator]],
      isOnline: [true, [Validators.required]],
      images: ['', [Validators.required, ValidateImages(1, 5)]],
      totalStockQuantity: [0, [Validators.required]],
      options: this.fb.array([]),
      extraInfos: this.fb.array([]),
      color: this.fb.group({
        hex: ['#1677ff', [Validators.required]],
        name: ['', [Validators.required]],
      }),
    });
  }
  get color() {
    return this.baseForm?.get('color') as FormGroup;
  }

  get options() {
    return this.baseForm?.get('options') as FormArray;
  }

  get extraInfos() {
    return this.baseForm?.get('extraInfos') as FormArray;
  }

  get categories() {
    return this.baseForm?.get('categories') as FormGroup;
  }

  newFormOption(value?: any) {
    return this.fb.group({
      _id: [value?._id || '', [Validators.nullValidator]],
      sizeType: [value?.sizeType || '', [Validators.required]],
      skuCode: [value?.skuCode || '', [Validators.required]],
      stockQuantity: [value?.stockQuantity || 0, [Validators.required]],
      price: [value?.price || 0, [Validators.required]],
      discount: [value?.discount || null, [Validators.nullValidator]],
      finalPrice: [0, [Validators.nullValidator]],
    });
  }

  addFormOption(value?: any) {
    this.options.push(this.newFormOption(value));
  }

  removeFormOption(index: number) {
    this.options.removeAt(index);
  }

  newFormExtraInfo(value?: any) {
    return this.fb.group({
      key: [value?.key || '', [Validators.required]],
      value: [value?.value || '', [Validators.required]],
    });
  }

  addFormExtraInfo(form: FormArray) {
    form.push(this.newFormExtraInfo());
  }

  removeFormExtraInfo(index: number) {
    this.extraInfos.removeAt(index);

    if (this.extraInfos.length <= 0) {
      this.showExtraInfo = false;
    }
  }

  onShowExtraInfoChange() {
    if (!this.showExtraInfo && this.extraInfos.length <= 0) {
      this.extraInfos.push(this.newFormExtraInfo());
    }
    this.showExtraInfo = !this.showExtraInfo;

    if (this.showExtraInfo) {
      this.extraInfos.enable();
    } else {
      this.extraInfos.disable();
    }
  }

  onShowColorChange() {
    this.showColor = !this.showColor;
    if (this.showColor) {
      this.color.enable();
    } else {
      this.color.disable();
    }
  }

  observeFormChanges() {
    this.imageService.uploadOnError$
      .pipe(takeUntilDestroyed())
      .subscribe((item) => {
        this.isValidForm = false;
      });

    this.baseForm.valueChanges.pipe(takeUntilDestroyed()).subscribe(() => {
      this.isValidForm = this.baseForm.valid;
    });

    this.options.valueChanges.pipe(takeUntilDestroyed()).subscribe(() => {
      this.calculateFinalPrices();
    });
  }

  private calculateFinalPrices() {
    this.options.controls.forEach((option) => {
      const percentage =
        this.discounts()?.find((item) => item._id == option.value?.discount)
          ?.percentage || 0;

      option
        .get('finalPrice')!
        .setValue(
          percentage > 0
            ? option.value.price * (1 - percentage / 100)
            : option.value.price,
          { emitEvent: false },
        );
    });
  }

  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? (this.isValidForm = false)
      : (this.isValidForm = true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };

  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  onDataSaveClick() {
    switch (this.crudMode) {
      case crudActionType.create:
        this.onDataSubmit();
        break;
      case crudActionType.update:
        this.onDataUpdate();
        break;
    }
  }

  override onDataSubmit() {
    this.loading = true;
    const product = this.baseForm.getRawValue();
    console.log('onDataSubmit', product);
    if (!this.showColor) delete product.color;
    if (!this.showExtraInfo) product.extraInfos = [];
    FormUtils.removeObjectNullProperties(product);
    this.messageService.addLoadingMessage('loading');
    this.productService.create(product).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('PRODUCTS.createSuccess');
        this.loading = false;
        this.router.navigate(['products', res.data!._id]);
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  override onDataUpdate() {
    this.loading = true;
    const product = this.baseForm.value;
    console.log('onDataUpdate', product);
    if (!this.showColor) delete product.color;
    FormUtils.removeObjectNullProperties(product);
    if (!this.showExtraInfo) product.extraInfos = [];
    this.messageService.addLoadingMessage('loading');
    this.productService.update(this.productId, product).subscribe({
      next: () => {
        this.messageService.addSuccessMessage('PRODUCTS.updateSuccess');
        this.loading = false;
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  override setCrudMode() {
    switch (this.crudMode) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }

  override setCreateMode() {
    switch (this.customMode) {
      case customModeType.createVariant:
        this.baseForm.reset();
        this.initForm();
        this.color.disable();
        this.baseForm.get('name')!.disable();
        this.addFormOption(this.newFormOption());
        this.saveButtonTitle = 'PRODUCTS.saveProduct';
        this.abortButtonTitle = 'abort';

        this.productService.readOne(this.productId).subscribe({
          next: (res) => {
            this.baseForm.get('name')!.setValue(this.product()!.name);
            this.baseForm.get('groupId')!.setValue(this.product()!.groupId);
            this.baseForm.get('variantName')!.reset();
            this.productService.setProduct(undefined);
          },
          error: () => {
            this.router.navigateByUrl('/products');
          },
        });
        break;
      default:
        this.productService.setProduct(undefined);
        this.productService.setLInkedProducts(undefined);
        this.baseForm.reset();
        this.initForm();
        this.color.disable();
        this.addFormOption(this.newFormOption());
        this.saveButtonTitle = 'PRODUCTS.saveProduct';
        this.abortButtonTitle = 'abort';
        break;
    }
    this.loading = false;
  }
  override setUpdateMode() {
    this.saveButtonTitle = 'PRODUCTS.updateProduct';
    this.abortButtonTitle = 'PRODUCTS.deleteProduct';

    this.productService.readOne(this.productId).subscribe({
      next: (res) => {
        this.fillFormData();
        this.initUploadService();
        this.loading = false;
      },
      error: () => {
        this.router.navigateByUrl('/products');
      },
    });
  }

  onAbortClick() {
    switch (this.crudMode) {
      case crudActionType.create:
        if (this.customMode !== customModeType.createVariant) {
          this.modalService.confirmDelete({
            confirmFn: () => {
              this.router.navigateByUrl('/products');
            },
            title: 'PRODUCTS.confirmDeleteTitle',
            subtitle: 'PRODUCTS.confirmDeleteSubtitle',
          });
        } else {
          this.router.navigateByUrl('/products');
        }
        break;

      case crudActionType.update:
        this.loading = true;
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.productService.delete(this.productId).subscribe({
              next: () => {
                this.loading = false;
                this.router.navigateByUrl('/products');
                this.messageService.addSuccessMessage('PRODUCT.deleteSuccess');
              },
            });
          },
          title: 'PRODUCTS.confirmDeleteTitle',
          subtitle: 'PRODUCTS.confirmDeleteSubtitle',
          cancelFn: () => (this.loading = false),
        });
        break;
    }
  }

  fillFormData() {
    this.baseForm.get('_id')?.setValue(this.product()!._id);
    this.baseForm.get('groupId')?.setValue(this.product()!.groupId);
    this.baseForm.get('name')?.setValue(this.product()!.name);
    this.baseForm.get('variantName')?.setValue(this.product()!.variantName);
    this.baseForm.get('categories')?.setValue(this.product()!.categories);
    this.baseForm.get('description')?.setValue(this.product()!.description);
    this.baseForm.get('images')?.setValue(this.product()!.images);
    this.baseForm
      .get('collectionGroup')
      ?.setValue(this.product()!.collectionGroup);

    this.options.clear();
    this.product()!.options.forEach((option) => {
      this.options.push(this.newFormOption(option));
    });
    this.calculateFinalPrices();

    if (this.product()!.extraInfos!.length > 0) {
      this.showExtraInfo = true;
      this.extraInfos.clear();
      this.product()!.extraInfos!.forEach((option) => {
        this.extraInfos.push(this.newFormExtraInfo(option));
      });
    } else {
      this.showExtraInfo = false;
      this.extraInfos.clear();
    }

    let categories = <string[]>[];
    this.product()!.categories.forEach((category: ICategory) => {
      categories.push(category._id);
    });
    this.categories.setValue(categories);

    if (!!this.product()!.color) {
      this.color.enable();
      this.showColor = true;
      this.color.get('name')!.setValue(this.product()!.color!.name);
      this.color.get('hex')!.setValue(this.product()!.color!.hex);
    } else {
      this.showColor = false;
      this.color.disable();
    }
  }

  onExampleDataClick() {}

  // TEST ONLY
  // onExampleDataClick() {
  //   this.baseForm.get('name')?.setValue(PRODUCT_EXAMPLE_POLO.name);
  //   this.baseForm
  //     .get('variantName')
  //     ?.setValue(PRODUCT_EXAMPLE_POLO.variantName);
  //   this.baseForm.get('isOnline')?.setValue(PRODUCT_EXAMPLE_POLO.isOnline);
  //   this.baseForm
  //     .get('description')
  //     ?.setValue(PRODUCT_EXAMPLE_POLO.description);

  //   if (PRODUCT_EXAMPLE_POLO.color) {
  //     this.showColor = true;
  //     this.baseForm
  //       .get('color.name')
  //       ?.setValue(PRODUCT_EXAMPLE_POLO.color?.name);
  //     this.baseForm.get('color.hex')?.setValue(PRODUCT_EXAMPLE_POLO.color?.hex);
  //   }

  //   this.options.clear();
  //   PRODUCT_EXAMPLE_POLO.options.forEach((option) => {
  //     this.options.push(this.newFormOption(option));
  //   });
  //   this.calculateFinalPrices();

  //   if (PRODUCT_EXAMPLE_POLO.color) {
  //     this.showColor = true;
  //     this.color.get('name')!.setValue(PRODUCT_EXAMPLE_POLO.color.name);
  //     this.color.get('hex')!.setValue(PRODUCT_EXAMPLE_POLO.color.hex);
  //   }
  // }
}
