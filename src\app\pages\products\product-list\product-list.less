.grid {
    display: grid;
    grid-template-columns: auto auto auto;
    column-gap: 26px;

    .name,
    .price,
    .quantity {
        font-weight: bold;
        margin-bottom: 4px;

    }

    .option {

        span[nz-icon] {
            color: #57758f;
        }

        &.quantity,
        &.price {
            display: flex;
            justify-content: space-between;
        }
    }

    .icon {
        margin-right: 0.5rem;
    }
}