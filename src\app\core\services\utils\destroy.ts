import { Injectable, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';

/**
 * Servizio che semplifica la gestione della distruzione di componenti Angular tramite Subject.
 * Emette un valore e completa lo stream quando il componente viene distrutto.
 */
@Injectable()
export class DestroyService extends Subject<void> implements OnDestroy {
  /**
   * Emette un valore e completa lo stream quando il componente viene distrutto.
   * @returns void
   */
  ngOnDestroy(): void {
    this.next();
    this.complete();
  }
}
