import { roleType } from '@models/enums/role';

export interface IStaff {
  id: string;
  email: string;
  name: string;
  surname: string;
  role: roleType;
  owner: boolean;
  isEnabled: boolean;
  isActive: boolean;
  isDeleted?: boolean;
  permissions?: string[];
  googleToken?: boolean;
  googleCalendarId?: string;
}

export interface IPartialStaff
  extends Pick<IStaff, 'id' | 'name' | 'surname' | 'email'> {}
