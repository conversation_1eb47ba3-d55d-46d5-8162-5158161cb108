import { Routes } from '@angular/router';
import { crudActionType } from '@models/enums/crud-action-type';
import { StaffCreateUpdate } from './staff-create-update/staff-create-update';
import { StaffList } from './staff-list/staff-list';

export const STAFF_ROUTES: Routes = [
  {
    path: '',
    component: StaffList,
  },
  {
    path: 'create',
    component: StaffCreateUpdate,
    data: { crudType: crudActionType.create },
  },
  {
    path: ':id',
    component: StaffCreateUpdate,
    data: { crudType: crudActionType.update },
  },

  // {
  //   path: ":id",
  //   component: StaffOverviewComponent,
  //   children: [
  //     { path: "", redirectTo: "info", pathMatch: "full" },
  //     {
  //       path: "info",
  //       component: StaffCreateUpdateComponent,
  //       data: { crudType: crudActionType.update },
  //     },
  //     {
  //       path: "working-hours",
  //       loadComponent: () =>
  //         import(
  //           "./staff-open-hours/staff-working-hours/staff-working-hours.component"
  //         ).then((m) => m.StaffWorkingHoursComponent),
  //       canDeactivate: [
  //         canDeactivateFormGuard(
  //           (c: StaffWorkingHoursComponent) =>
  //             c.workingHoursComponent().baseForm
  //         ),
  //       ],
  //     },
  //     {
  //       path: "closed-days",
  //       loadComponent: () =>
  //         import(
  //           "./staff-open-hours/staff-closed-days/staff-closed-days.component"
  //         ).then((m) => m.StaffClosedDaysComponent),
  //       canDeactivate: [
  //         canDeactivateFormGuard(
  //           (c: StaffClosedDaysComponent) =>
  //             c.closedDaysComponent().closedDaysForm
  //         ),
  //       ],
  //     },
  //     {
  //       path: "open-days",
  //       loadComponent: () =>
  //         import(
  //           "./staff-open-hours/staff-open-days/staff-open-days.component"
  //         ).then((m) => m.StaffOpenDaysComponent),
  //       canDeactivate: [
  //         canDeactivateFormGuard(
  //           (c: StaffOpenDaysComponent) => c.openDaysComponent().openDaysForm
  //         ),
  //       ],
  //     },
  //     {
  //       path: "bookings",
  //       loadComponent: () =>
  //         import("./staff-bookings/staff-bookings.component").then(
  //           (m) => m.StaffBookingsComponent
  //         ),
  //     },
  //   ],
  // },
];
