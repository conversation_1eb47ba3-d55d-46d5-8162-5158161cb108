import { Ng<PERSON><PERSON>, SlicePipe } from '@angular/common';
import { Component, inject, signal, Signal } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { HeaderService } from '@core/services/utils/header';
import { LayoutService } from '@core/services/utils/layout';
import { currentSectionType } from '@models/enums/current-section';
import { TranslateModule } from '@ngx-translate/core';

import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BreadcrumbService } from '@core/services/utils/breadcrumb';
import { CategoriesHeader } from '@shared/header-extra/categories-header/categories-header';
import { DashboardHeader } from '@shared/header-extra/dashboard-header/dashboard-header';
import { ProductsHeader } from '@shared/header-extra/products-header/products-header';
import { StaffDetailHeader } from '@shared/header-extra/staff-header/staff-detail-header/staff-detail-header';
import { StaffListHeader } from '@shared/header-extra/staff-header/staff-list-header/staff-list-header';
import { NzAvatarComponent } from 'ng-zorro-antd/avatar';
import { NzDividerComponent } from 'ng-zorro-antd/divider';
import {
  NzPageHeaderComponent,
  NzPageHeaderExtraDirective,
  NzPageHeaderSubtitleDirective,
  NzPageHeaderTitleDirective,
} from 'ng-zorro-antd/page-header';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    NzPageHeaderComponent,
    NzAvatarComponent,
    NzDividerComponent,
    NgClass,
    TranslateModule,
    SlicePipe,
    NzPageHeaderTitleDirective,
    NzPageHeaderSubtitleDirective,
    NzPageHeaderExtraDirective,
    DashboardHeader,
    ProductsHeader,
    CategoriesHeader,
    StaffListHeader,
    StaffDetailHeader,
  ],
  templateUrl: './header.html',
  styleUrl: './header.less',
})
export class HeaderComponent {
  // SERVICES
  private router = inject(Router);
  private layoutService = inject(LayoutService);
  private headerService = inject(HeaderService);
  private breadcrumbService = inject(BreadcrumbService);

  // PROPERTIES
  protected urls = signal<string[]>([]);
  protected currentUrl: string;
  protected icon!: string;
  protected currentMenu = this.layoutService.currentMenu$;
  protected currentSection: Signal<currentSectionType | undefined>;
  protected currentSectionType = currentSectionType;
  protected breadcrumbData = this.breadcrumbService.breadcrumbData;

  constructor() {
    const parsedUrl = this.router.parseUrl(this.router.url);
    this.currentUrl =
      parsedUrl.root.children['primary']?.segments
        .map((segment) => segment.path)
        .join('/') || '';
    this.urls.set(this.currentUrl.split('/'));

    this.router.events.pipe(takeUntilDestroyed()).subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const parsedUrl = this.router.parseUrl(event.urlAfterRedirects);

        this.currentUrl =
          parsedUrl.root.children['primary']?.segments
            .map((segment) => segment.path)
            .join('/') || '';

        this.urls.set(this.currentUrl.split('/'));
      }
    });

    this.currentSection = this.headerService.data$;
  }

  gotoPage(url: string) {
    this.router.navigate([url]);
  }

  gotoSubpage(path: string) {
    let urls = [...this.urls()];
    const index = urls.findIndex((u) => u == path);
    urls = urls.slice(0, index + 1);
    let newUrl = '';
    urls.forEach((path, i) => {
      newUrl += `${path}`;
      if (i < urls.length - 1) newUrl += '/';
    });
    this.router.navigateByUrl(newUrl);
  }
}
