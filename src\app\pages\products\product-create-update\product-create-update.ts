import { NgClass, NgTemplateOutlet } from '@angular/common';
import { Component, inject, Signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { NullValuePipe } from '@core/pipes/null-value.pipe';
import { ProductsService } from '@core/services/http/products';
import { HeaderService } from '@core/services/utils/header';
import { crudActionType, customModeType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { IProduct } from '@models/interfaces/product';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzColDirective, NzRowDirective } from 'ng-zorro-antd/grid';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import {
  NzListComponent,
  NzListEmptyComponent,
  NzListItemComponent,
  NzListItemMetaAvatarComponent,
  NzListItemMetaComponent,
  NzListItemMetaDescriptionComponent,
  NzListItemMetaTitleComponent,
} from 'ng-zorro-antd/list';
import { switchMap, tap } from 'rxjs';

@Component({
  selector: 'app-product-create-update',
  standalone: true,
  imports: [
    NzListComponent,
    NzListItemComponent,
    NzListItemMetaComponent,
    NzListItemMetaAvatarComponent,
    NzListItemMetaTitleComponent,
    NzListItemMetaDescriptionComponent,
    NzListEmptyComponent,
    NzIconDirective,
    RouterOutlet,
    TranslateModule,
    NzButtonComponent,
    NzRowDirective,
    NzColDirective,
    NullValuePipe,
    NgTemplateOutlet,
    NgClass,
  ],
  templateUrl: './product-create-update.html',
  styleUrl: './product-create-update.less',
})
export class ProductCreateUpdateComponent {
  // SERVICES
  private productService = inject(ProductsService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private headerService = inject(HeaderService);

  protected loading: boolean = true;
  protected crudMode: crudActionType = crudActionType.create;
  protected customMode: customModeType = null;
  protected customModeType = customModeType;
  protected crudActionType = crudActionType;
  protected product: Signal<IProduct> = this.productService.product$;
  protected linkedProducts: Signal<IProduct[]> =
    this.productService.linkedProducts$;
  protected productId: string;

  constructor() {
    this.headerService.setCurrentSection(currentSectionType.productDetail);

    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.productId = <string>params.get('id');
          return this.route.data.pipe(
            tap((data) => {
              console.log('PRODUCT CREATE UPDATE DATA', data);
              this.crudMode = <crudActionType>data['crudType'];
              this.customMode = <customModeType>data['customMode'];
              this.loading = false;
            }),
          );
        }),
      )
      .subscribe();
  }

  onProductClick(productId: string) {
    this.router.navigate(['products', productId]);
  }

  onAddVariantClick() {
    this.router.navigateByUrl(`products/${this.productId}/variant`);
  }
}
