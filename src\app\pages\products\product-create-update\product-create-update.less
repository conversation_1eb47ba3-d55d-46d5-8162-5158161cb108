@import 'mixin';

.themeMixin({
    :host {
        .divider {
            &-center {
                padding: 0 2rem;
            }

            &-left {
                padding-left: 2rem;
                border-left: 1px solid rgba(255, 255, 255, 0.12);

            }

            &-right {
                padding-right: 1.5rem;
                border-right: 1px solid rgba(255, 255, 255, 0.12);
                height: 86%;
            }
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
        }

        .variants-container {
            // height: calc(100vh - 300px);
            overflow-y: auto;
        }

        .border-bottom {
            border-bottom: 1px solid lightgray;
        }

        nz-list-item-meta {
            padding: 0.5rem;

            &.active {
                border-radius: 4px;
                background: rgba(@primary-color, .2);
            }

            & a.active {
                color: @primary-color
            }

        }
    }
});