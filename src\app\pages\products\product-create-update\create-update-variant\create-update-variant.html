<div class="container">
  <div class="form">
    <nz-spin [nzSpinning]="loading">
      <form nz-form [formGroup]="baseForm" nzLayout="vertical">
        <div nz-row [nzGutter]="16">
          <!-- LEFT COL -->
          <div nz-col [nzSpan]="13">
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="16">
                <app-input-generic
                  [parentForm]="baseForm"
                  [controlName]="'name'"
                  [label]="'PRODUCTS.productName' | translate"
                  [placeholder]="'PRODUCTS.namePlaceholder' | translate"
                ></app-input-generic>
              </div>
              <div nz-col [nzSpan]="8">
                <app-input-generic
                  [parentForm]="baseForm"
                  [controlName]="'variantName'"
                  [label]="'PRODUCTS.variantName' | translate"
                  [placeholder]="'PRODUCTS.varianNamePlaceholder' | translate"
                ></app-input-generic>
              </div>
            </div>

            <app-input-textarea
              [parentForm]="baseForm"
              [controlName]="'description'"
              [size]="{ minRows: 6, maxRows: 12 }"
              [label]="'PRODUCTS.description' | translate"
              [placeholder]="'PRODUCTS.descriptionPlaceholder' | translate"
            ></app-input-textarea>

            <!-- IMAGES -->
            <nz-form-item [formGroup]="baseForm" style="margin-bottom: 1.3rem">
              <nz-form-control>
                <div class="clearfix">
                  <nz-form-label [nzRequired]="false">
                    <i
                      nz-icon
                      class="info-icon"
                      nz-popover
                      [nzPopoverTitle]="'limits.limit' | translate"
                      [nzPopoverContent]="tplImageInfo"
                      nzType="info-circle"
                    ></i>
                    <strong>{{ "images" | translate }}</strong>
                    <ng-template #tplImageInfo>
                      <div class="info-image">
                        <strong>{{ "limits.width" | translate }}: </strong>
                        <div class="min-max">
                          <span>min {{ Limits.width.min }}px</span>
                          <span>max {{ Limits.width.max }}px</span>
                        </div>
                        <strong>{{ "limits.heigth" | translate }}: </strong>
                        <div class="min-max">
                          <span>min {{ Limits.height.min }}px</span>
                          <span>max {{ Limits.height.max }}px</span>
                        </div>
                      </div>
                    </ng-template>
                  </nz-form-label>
                  <nz-upload
                    [nzAction]="'http://localhost:4201/api/fakeImage'"
                    [nzCustomRequest]="uploadRequest"
                    [(nzFileList)]="FileList"
                    [nzShowButton]="FileList.length < 5"
                    [nzFileType]="
                      'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
                    "
                    [nzHeaders]="setMediaUploadHeaders"
                    [nzPreview]="handlePreview"
                    [nzRemove]="removeItem"
                    [nzShowUploadList]="showUploadList"
                    nzListType="picture-card"
                  >
                    <div>
                      <i nz-icon nzType="plus"></i>
                      <div style="margin-top: 8px">Carica</div>
                    </div>
                  </nz-upload>
                  <nz-modal
                    [nzVisible]="previewVisible"
                    [nzContent]="modalContent"
                    [nzFooter]="null"
                    (nzOnCancel)="previewVisible = false"
                  >
                    <ng-template #modalContent>
                      <img [src]="previewImage" [ngStyle]="{ width: '100%' }" />
                    </ng-template>
                  </nz-modal>
                </div>
              </nz-form-control>
            </nz-form-item>

            <div nz-row [nzAlign]="'top'">
              <div nz-row class="w-100">
                <div nz-col [nzSpan]="12">
                  <div nz-row class="w-100">
                    <div style="margin-bottom: 1.3rem">
                      <app-toggle-checkbox
                        [label]="'PRODUCTS.color' | translate"
                        [checked]="showColor"
                        (onCheckboxChange)="onShowColorChange()"
                      ></app-toggle-checkbox>
                    </div>
                  </div>

                  @if (showColor) {
                    <div nz-row [nzGutter]="32">
                      <div nz-col [nzSpan]="4">
                        <div [formGroup]="color">
                          <nz-color-picker
                            formControlName="hex"
                          ></nz-color-picker>
                        </div>
                      </div>

                      <div nz-col [nzSpan]="20">
                        <app-input-generic
                          [parentForm]="color"
                          [controlName]="'name'"
                          [placeholder]="
                            'PRODUCTS.colorPlaceholder' | translate
                          "
                        ></app-input-generic>
                      </div>
                    </div>
                  }
                </div>
              </div>
            </div>

            <!-- EXTRA OPTIONS -->
            <div nz-row>
              <div nz-row style="margin-bottom: 1.3rem">
                <app-toggle-checkbox
                  [label]="'PRODUCTS.additionalInfo' | translate"
                  [checked]="showExtraInfo"
                  (onCheckboxChange)="onShowExtraInfoChange()"
                ></app-toggle-checkbox>
              </div>

              @if (showExtraInfo) {
                @for (option of extraInfos.controls; track $index) {
                  <div
                    nz-row
                    [nzGutter]="16"
                    class="w-100"
                    [nzAlign]="'middle'"
                  >
                    <div nz-col [nzSpan]="11">
                      <app-input-generic
                        [parentForm]="option"
                        [controlName]="'key'"
                        [label]="'PRODUCTS.propertyName' | translate"
                        [placeholder]="
                          'PRODUCTS.propertyNamePlaceholder' | translate
                        "
                      ></app-input-generic>
                    </div>
                    <div nz-col [nzSpan]="11">
                      <app-input-generic
                        [parentForm]="option"
                        [controlName]="'value'"
                        [label]="'PRODUCTS.propertyValue' | translate"
                        [placeholder]="
                          'PRODUCTS.propertyValuePlaceholder' | translate
                        "
                      ></app-input-generic>
                    </div>
                    <div nz-col [nzSpan]="2" style="margin-top: 0.8rem">
                      <a
                        nz-popconfirm
                        nzPopconfirmTitle="Sicuro di voler cancellare?"
                        nzPopconfirmPlacement="bottom"
                        (nzOnConfirm)="removeFormExtraInfo($index)"
                        [nzOkDanger]="true"
                        (click)="$event.stopPropagation()"
                        class="error-color"
                      >
                        <span nz-icon nzType="delete"></span>
                      </a>
                    </div>
                  </div>
                }
              }
              @if (showExtraInfo) {
                <div nz-row [nzGutter]="16" class="w-100">
                  <div nz-col [nzSpan]="12"></div>
                  <div
                    nz-col
                    [nzSpan]="12"
                    style="display: flex; justify-content: flex-end"
                  >
                    <app-simple-button
                      [autoMinify]="false"
                      [title]="'PRODUCTS.addProperty' | translate"
                      [icon]="'plus'"
                      (onButtonClick)="addFormExtraInfo(extraInfos)"
                    ></app-simple-button>
                  </div>
                </div>
              }
            </div>
          </div>
          <!-- VERTICAL DIVIDER -->
          <div nz-col [nzSpan]="1" [nzFlex]="'center'">
            <nz-divider
              nzType="vertical"
              style="height: 100%; text-align: center"
            ></nz-divider>
          </div>
          <!-- RIGHT COL -->
          <div nz-col [nzSpan]="10">
            <div nz-row [nzGutter]="16">
              <div nz-col [nzSpan]="24">
                <app-input-checkbox
                  [parentForm]="baseForm"
                  [controlName]="'isOnline'"
                  [label]="'PRODUCTS.productStatus' | translate"
                  [optionList]="showProductStatus"
                  [name]="'PRODUCTS.showOnShop' | translate"
                >
                </app-input-checkbox>
              </div>
            </div>

            <div nz-row [nzGutter]="16" style="margin-bottom: 2.4rem">
              <div nz-col [nzSpan]="24">
                <app-input-select
                  [parentForm]="baseForm"
                  [allowClear]="true"
                  [serverSearch]="true"
                  [maxTagCount]="5"
                  [controlName]="'categories'"
                  [label]="'PRODUCTS.categories' | translate"
                  [optionList]="categoriesList()"
                  [configKey]="{ label: 'name', value: '_id' }"
                  [mode]="'multiple'"
                  [placeholder]="'PRODUCTS.categoriesPlaceholder' | translate"
                ></app-input-select>
              </div>
            </div>

            <nz-divider
              [nzText]="tplText"
              [nzOrientation]="'left'"
              class="primary-color-border"
            ></nz-divider>
            <ng-template #tplText>
              <div class="primary-color">
                <span
                  nz-icon
                  nzType="setting"
                  style="margin-right: 0.5rem"
                ></span>
                {{ "PRODUCTS.options" | translate }}
              </div>
            </ng-template>

            <div nz-row [nzGutter]="16" style="margin-bottom: 1.3rem">
              <div nz-col [nzSpan]="24">
                @for (option of options.controls; track $index) {
                  <nz-collapse>
                    <nz-collapse-panel
                      [nzHeader]="tplCollapseHeader"
                      [nzActive]="crudMode == 'create' ? true : false"
                      [nzExtra]="tplCollapseExtra"
                    >
                      <ng-template #tplCollapseHeader>
                        <div class="collapse-header">
                          <div
                            class="option"
                            nz-tooltip
                            [nzTooltipTitle]="option.value.name"
                          >
                            <span
                              nz-icon
                              nzType="client-ui:size"
                              style="margin-right: 0.7rem"
                            ></span>
                            <span class="option-name">
                              {{
                                option.value.sizeType
                                  ? option.value.sizeType
                                  : "/"
                              }}
                            </span>
                          </div>
                          <div class="price">
                            @if (option.value.discount) {
                              <app-badge-discount
                                [price]="option.value.finalPrice"
                                style="margin-right: -4px; margin-top: -0.3rem"
                              ></app-badge-discount>
                            } @else {
                              {{ option.value.price | currency: "EUR" }}
                            }
                            <span
                              nz-icon
                              nzType="client-ui:price"
                              class="icon"
                            ></span>
                          </div>
                          <div class="quantity">
                            {{ option.value.stockQuantity }}
                            <span
                              nz-icon
                              nzType="client-ui:quantity"
                              class="icon"
                            ></span>
                          </div>
                        </div>
                      </ng-template>

                      <ng-template #tplCollapseExtra>
                        @if (options.length > 1) {
                          <a
                            nz-popconfirm
                            [nzPopconfirmTitle]="
                              'POPOVER.confirmDeleteTitle' | translate
                            "
                            nzPopconfirmPlacement="bottom"
                            (nzOnConfirm)="removeFormOption($index)"
                            [nzOkDanger]="true"
                            (click)="$event.stopPropagation()"
                            class="error-color"
                          >
                            <span
                              nz-icon
                              nzType="delete"
                              style="margin-left: 1rem"
                            ></span>
                          </a>
                        }
                      </ng-template>

                      <app-input-generic
                        [parentForm]="option"
                        [controlName]="'sizeType'"
                        [label]="'PRODUCTS.optionName'"
                      ></app-input-generic>
                      <app-input-generic
                        [parentForm]="option"
                        [controlName]="'skuCode'"
                        [label]="'Codice SKU'"
                      ></app-input-generic>
                      <div nz-row [nzGutter]="16">
                        <div nz-col [nzSpan]="12">
                          <app-input-number
                            [parentForm]="option"
                            [controlName]="'stockQuantity'"
                            [label]="'PRODUCTS.quantity'"
                            [width]="'100%'"
                            [minNumber]="0"
                            [step]="1"
                          ></app-input-number>
                        </div>
                        <div nz-col [nzSpan]="12">
                          <app-input-number
                            [parentForm]="option"
                            [controlName]="'price'"
                            [label]="'PRODUCTS.price'"
                            [prefix]="'€'"
                            [step]="1"
                            [minNumber]="0"
                            [precision]="2"
                            [formatDecimal]="true"
                          ></app-input-number>
                        </div>
                      </div>

                      <div nz-row [nzGutter]="16" style="margin-bottom: 2.4rem">
                        <div nz-col [nzSpan]="12">
                          <!-- <app-input-select
                            [parentForm]="option"
                            [controlName]="'discount'"
                            [label]="'PRODUCTS.discount'"
                            [optionList]="discounts() || []"
                            [placeholder]="'PRODUCTS.selectPlaceholder'"
                            [configKey]="{ label: 'name', value: '_id' }"
                            [allowClear]="true"
                          ></app-input-select> -->
                        </div>
                        <div nz-col [nzSpan]="12">
                          <nz-form-label>{{
                            "PRODUCTS.finalPrice" | translate
                          }}</nz-form-label>
                          <div class="final-price">
                            {{ option.value.finalPrice | currency: "EUR" }}
                          </div>
                        </div>
                      </div>
                    </nz-collapse-panel>
                  </nz-collapse>
                }
              </div>
            </div>
            <div nz-row class="w-100">
              <div nz-col [nzSpan]="12"></div>
              <div
                nz-col
                [nzSpan]="12"
                style="display: flex; justify-content: flex-end"
              >
                <app-simple-button
                  [autoMinify]="false"
                  [title]="'PRODUCTS.addOption' | translate"
                  [icon]="'plus'"
                  (onButtonClick)="addFormOption(options)"
                ></app-simple-button>
              </div>
            </div>
          </div>
        </div></form
    ></nz-spin>
  </div>
  <nz-divider></nz-divider>

  <div class="text-right">
    <nz-space>
      <div style="margin-right: 1rem">
        <app-simple-button
          [title]="'Riempi form (dati esempio)'"
          [autoMinify]="false"
          (onButtonClick)="onExampleDataClick()"
        ></app-simple-button>
      </div>
      <button *nzSpaceItem nz-button (click)="onAbortClick()">
        {{ abortButtonTitle | translate }}
      </button>
      <button
        *nzSpaceItem
        nz-button
        nzType="primary"
        (click)="onDataSaveClick()"
        [disabled]="!isValidForm || loading"
      >
        {{ saveButtonTitle | translate }}
        <i nz-icon nzType="plus"></i>
      </button>
    </nz-space>
  </div>
</div>
