@import 'mixin';

.themeMixin({
  :host {

    .app-layout {
      height: 100vh;
      width: 100vw;
    }

    .menu-sidebar {
      position: relative;
      z-index: 10;
      min-height: 100vh;
      transition: ease-in-out width 0.3s;
      // transition: none;
    }

    .ant-layout-sider-children {
      background: @dark;
    }

    .sidebar-logo {
      position: relative;
      height: 64px;
      overflow: hidden;
      line-height: 64px;
      transition: ease-in all 0.3s;
      display: flex;
      align-items: center;
      justify-content: space-between;

      a.trigger {
        padding: 8px 0;
        font-size: 20px;
        cursor: pointer;
        transition: ease-in all 0.3s;

        &.collapsed {
          position: fixed;
          left: 84px;
          padding: 8px;
        }

        &:hover {
          color: fade(@primary-color, 70%);
        }
      }

      & img.logo-collapsed {
        display: inline-block;
        //background-color: @primary-color;
        padding: 12px;
        width: 90%;
        border-radius: 16px;
        overflow: hidden;
        // content: url('/assets/images/logo/logo.svg');
      }

      & img.logo {
        // transition: ease-in all 0.3s;
        display: inline-block;
        padding: 12px;
        width: 90%;
        //background-color: @primary-color;
        // content: url('/assets/images/logo/logo.svg');
      }
    }

    nz-header {
      padding: 0;
      width: 100%;
      z-index: 2;
    }

    .app-header {
      position: relative;
      padding: 0 16px;
    }

    .main-menu {
      z-index: 9;
      border: 0;
      height: calc(100vh - 64px);
      overflow-y: auto;
      overflow-x: hidden;

      background: @sider-menu;

      .menu-item {
        font-weight: 600;

        &.help-center {
          position: absolute;
          bottom: 6px;

        }

        &.is-collapsed {
          max-width: 64px;
        }
      }

      .info-box {
        position: absolute;
        display: flex;
        flex-direction: column;
        gap: 16px;
        width: 100%;
        bottom:67px;
        left: 0;
        background-color: @info-box-bg;
        margin: 0 8px;
        padding: 14px;
        width: calc(100% - 16px);
        border-radius: 10px;

        .limits-section {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }



      }

    }

    nz-content {
      margin: 12px;
      position: relative;

      #triangle-topleft {
        width: 0;
        height: 0;
        border-top: 26px solid @triangle-radius;
        border-right: 26px solid transparent;
        position: absolute;
        left: -22px;
        top: -22px;
      }

      #circle {
        width: 12px;
        height: 12px;
        background: @circle-radius;
        border-radius: 12px;
        position: absolute;
        left: -12px;
        top: -12px;
      }

      .inner-content {
        padding: 24px;
        padding-top: 0;
        background: @layout-main-page;
        height: 100%;
        // overflow-y: auto;
        overflow: hidden;
        border-radius: 4px;
        position: relative;
      }
    }


    .app-header {
      padding: 0;
    }

    @media screen and (max-width: 768px) {
      .app-layout nz-sider.menu-sidebar {
        display: none
      }

      nz-content .inner-content {
        padding: 24px 12px;
        padding-top: 0
      }
    }
  }
});

.ant-divider-horizontal.ant-divider-with-text {
  font-size: 12px;
}