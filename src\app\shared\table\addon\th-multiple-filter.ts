import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  inject,
  input,
  output,
  SimpleChanges,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DestroyService } from '@core/services/utils/destroy';
import { TranslateModule } from '@ngx-translate/core';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzCheckboxComponent } from 'ng-zorro-antd/checkbox';
import { arraysEqual } from 'ng-zorro-antd/core/util';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzInputDirective, NzInputGroupComponent } from 'ng-zorro-antd/input';
import { NzMenuItemComponent } from 'ng-zorro-antd/menu';
import { NzRadioComponent } from 'ng-zorro-antd/radio';
import { NzTableFilterList } from 'ng-zorro-antd/table';

@Component({
  selector: 'th-multiple-filter',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    TranslateModule,
    NzButtonComponent,
    NzInputDirective,
    NzInputGroupComponent,
    NzIconDirective,
    NzMenuItemComponent,
    NzRadioComponent,
    NzCheckboxComponent,
  ],
  providers: [DestroyService],
  template: `
    <div class="ant-table-filter-dropdown">
      @if (filtersSearchable()) {
        <nz-input-group
          [nzSuffix]="tplInputClear"
          class="th-multiple-filter-border"
        >
          <input
            nzBorderless
            style="margin:0; width:100%"
            type="text"
            nz-input
            [placeholder]="'TABLE.search' | translate"
            [(ngModel)]="searchedValue"
            (ngModelChange)="search($event)"
          />
        </nz-input-group>
        <ng-template #tplInputClear>
          @if (searchedValue) {
            <i
              nz-icon
              class="ant-input-clear-icon"
              nzTheme="fill"
              nzType="close-circle"
              (click)="clear()"
            ></i>
          }
        </ng-template>
      }
      <ul nz-menu>
        @for (f of listOfParsedFilter; track trackByValue($index, f)) {
          <li nz-menu-item [nzSelected]="f.checked" (click)="check(f)">
            @if (!filterMultiple()) {
              <label
                nz-radio
                [ngModel]="f.checked"
                (ngModelChange)="check(f)"
              ></label>
            }
            @if (filterMultiple()) {
              <label
                nz-checkbox
                [ngModel]="f.checked"
                (ngModelChange)="check(f)"
              ></label>
            }
            <span>{{ f.text | translate }}</span>
          </li>
        }
      </ul>
      <div class="ant-table-filter-dropdown-btns" style="padding-left: 8px;">
        <button
          nz-button
          nzType="default"
          nzSize="small"
          style="margin-right: 8px;"
          (click)="reset()"
          [disabled]="!isChecked"
        >
          {{ 'TABLE.reset' | translate }}
        </button>
        <button nz-button nzType="primary" nzSize="small" (click)="confirm()">
          {{ 'TABLE.search' | translate }}
        </button>
      </div>
    </div>
  `,
  host: { class: 'ant-table-filter-column' },
})
export class ThMultipleFilterComponent {
  // SERVICES
  private elementRef = inject(ElementRef);

  // INPUTS
  readonly filtersSearchable = input<boolean>(false);
  readonly filterMultiple = input(true);
  readonly listOfFilter = input<NzTableFilterList>([]);

  // OUTPUTS
  readonly onFilterChange = output<any[] | any>();

  // VARIABLES
  isChecked = false;
  listOfParsedFilter: NzThItemInterface[] = [];
  listOfParsedFilterToBackup: NzThItemInterface[];
  listOfSelectedFilterToSearch: NzThItemInterface[];
  listOfChecked: any[] = [];
  searchedValue: any;

  constructor() {
    this.elementRef.nativeElement.classList.add('ant-table-filter-column');
  }

  ngOnChanges(changes: SimpleChanges): void {
    const { listOfFilter } = changes;
    const listOfFilterValue = this.listOfFilter();
    if (listOfFilter && listOfFilterValue && listOfFilterValue.length) {
      this.listOfParsedFilter = this.parseListOfFilter(listOfFilterValue);
      //Creo il backup
      this.listOfParsedFilterToBackup = [...this.listOfParsedFilter];
      this.listOfSelectedFilterToSearch = [...this.listOfParsedFilterToBackup];
      this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);
    }
  }

  trackByValue(_: number, item: NzThItemInterface): any {
    return item.value;
  }

  check(filter: NzThItemInterface): void {
    if (this.filterMultiple()) {
      this.listOfSelectedFilterToSearch.map((item) => ({
        ...item,
        checked: item === filter,
      }));
      this.listOfParsedFilter = this.listOfParsedFilter.map((item) => {
        if (item === filter) {
          return { ...item, checked: !filter.checked };
        } else {
          return item;
        }
      });
      filter.checked = !filter.checked;
      this.isChecked = this.getCheckedStatus(this.listOfSelectedFilterToSearch);
    } else {
      this.listOfParsedFilter = this.listOfParsedFilter.map((item) => ({
        ...item,
        checked: item === filter,
      }));
      this.listOfParsedFilterToBackup = this.listOfParsedFilterToBackup.map(
        (item) => ({
          ...item,
          checked: item === filter,
        }),
      );
      this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);
    }
  }

  confirm(): void {
    this.emitFilterData();
    setTimeout(() => {
      this.clear();
    }, 500);
  }

  reset(): void {
    this.listOfParsedFilter = this.parseListOfFilter(
      this.listOfParsedFilterToBackup,
      true,
    );
    this.listOfSelectedFilterToSearch = [...this.listOfParsedFilter];
    this.listOfParsedFilterToBackup = [...this.listOfParsedFilter];
    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);
    this.emitFilterData();
    setTimeout(() => {
      this.clear();
    }, 500);
  }

  emitFilterData(): void {
    const filterMultiple = this.filterMultiple();
    if (filterMultiple)
      this.listOfParsedFilter = [...this.listOfSelectedFilterToSearch];
    const listOfChecked = this.listOfParsedFilter
      .filter((item) => item.checked)
      .map((item) => item.value);
    if (!arraysEqual(this.listOfChecked, listOfChecked)) {
      if (filterMultiple) {
        this.onFilterChange.emit(listOfChecked);
      } else {
        this.onFilterChange.emit(
          listOfChecked.length > 0 ? listOfChecked[0] : null,
        );
      }
    } else {
      this.onFilterChange.emit(null);
    }
  }

  parseListOfFilter(
    listOfFilter: NzTableFilterList,
    reset?: boolean,
  ): NzThItemInterface[] {
    return listOfFilter.map((item) => {
      const checked = reset ? false : !!item.byDefault;
      return { text: item.text, value: item.value, checked };
    });
  }

  getCheckedStatus(listOfParsedFilter: NzThItemInterface[]): boolean {
    return listOfParsedFilter.some((item) => item.checked);
  }

  search(substring: string) {
    if (substring) substring = substring.toLowerCase();
    this.listOfParsedFilter = [...this.listOfParsedFilterToBackup];
    this.listOfParsedFilter = this.listOfParsedFilter.filter(
      (elem) => elem.text.toLowerCase().includes(substring) == true,
    );
  }

  clear() {
    this.listOfParsedFilter = [...this.listOfParsedFilterToBackup];
    this.listOfSelectedFilterToSearch = [...this.listOfParsedFilterToBackup];
    this.searchedValue = null;
  }
}

interface NzThItemInterface {
  text: string;
  value: any;
  checked: boolean;
}
