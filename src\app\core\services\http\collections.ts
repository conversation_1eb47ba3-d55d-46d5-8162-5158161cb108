import { HttpClient } from '@angular/common/http';
import { inject, Injectable, signal } from '@angular/core';
import { environment } from '@env/environment';
import {
  IBaseResponse,
  IFindResponseMeta,
} from '@models/interfaces/base-response';
import { ICollection } from '@models/interfaces/collection';
import { CrudApiOperations } from '@models/interfaces/crud-api';
import { IRequestFilter, IRequestMeta } from '@shared/table/types/table.query';
import { Observable, tap } from 'rxjs';

/**
 * Servizio per la gestione delle collezioni di prodotti.
 * Fornisce CRUD e stato locale per le collezioni tramite API.
 */
@Injectable({
  providedIn: 'root',
})
export class CollectionsService
  implements CrudApiOperations<ICollection, string>
{
  private http = inject(HttpClient);

  // VARIABLES
  private _baseCollectionsApi = `${environment.api.collections}`;

  private _collection = signal<ICollection | undefined>(undefined);
  readonly collection$ = this._collection.asReadonly();

  private _collections = signal<ICollection[] | undefined>(undefined);
  readonly collections$ = this._collections.asReadonly();

  /**
   * Imposta la collezione attiva nello stato locale.
   * @param collection Collezione da impostare come attiva
   * @returns void
   */
  public setCollection(collection: ICollection | undefined): void {
    this._collection.set(collection);
  }

  /**
   * Imposta la lista delle collezioni nello stato locale.
   * @param collections Array di collezioni da impostare
   * @returns void
   */
  public setCollections(collections: ICollection[] | undefined): void {
    this._collections.set(collections);
  }

  /**
   * Crea una nuova collezione tramite API.
   * @param collection Oggetto collezione da creare
   * @returns Observable con la risposta della creazione
   */
  create(collection: ICollection) {
    return this.http
      .post<
        IBaseResponse<ICollection>
      >(`${this._baseCollectionsApi}`, collection)
      .pipe(tap(() => this.readAll().subscribe()));
  }

  /**
   * Aggiorna una collezione esistente tramite API.
   * @param collectionId ID della collezione da aggiornare
   * @param collection Nuovi dati della collezione
   * @returns Observable con la risposta dell'aggiornamento
   */
  update(collectionId: string, collection: ICollection) {
    return this.http.put<IBaseResponse<ICollection>>(
      `${this._baseCollectionsApi}/${collectionId}`,
      collection,
    );
  }

  /**
   * Elimina una collezione tramite API.
   * @param collectionId ID della collezione da eliminare
   * @returns Observable con la risposta dell'eliminazione
   */
  delete(collectionId: string) {
    return this.http
      .delete<void>(`${this._baseCollectionsApi}/${collectionId}`)
      .pipe(tap(() => this.readAll().subscribe()));
  }

  /**
   * Recupera una singola collezione tramite API e aggiorna lo stato locale.
   * @param collectionId ID della collezione da recuperare
   * @returns Observable con la collezione trovata
   */
  readOne(collectionId: string) {
    return this.http
      .get<
        IBaseResponse<ICollection>
      >(`${this._baseCollectionsApi}/${collectionId}`)
      .pipe(
        tap((value) => {
          this.setCollection(value.data!);
        }),
      );
  }

  /**
   * Recupera tutte le collezioni tramite API e aggiorna lo stato locale.
   * @returns Observable con la lista delle collezioni
   */
  readAll() {
    return this.http
      .get<IBaseResponse<ICollection[]>>(`${this._baseCollectionsApi}`)
      .pipe(
        tap((value) => {
          this.setCollections(value.data!);
        }),
      );
  }

  /**
   * Esegue una ricerca avanzata sulle collezioni tramite API.
   * @param meta Metadati di paginazione/ordinamento
   * @param filter Filtri di ricerca
   * @returns Observable con la lista delle collezioni e metadati
   */
  search(
    meta: IRequestMeta,
    filter: IRequestFilter[],
  ): Observable<IBaseResponse<ICollection[], IFindResponseMeta>> {
    return this.http.post<IBaseResponse<ICollection[], IFindResponseMeta>>(
      `${this._baseCollectionsApi}/search`,
      {
        meta,
        filter,
      },
    );
  }
}
