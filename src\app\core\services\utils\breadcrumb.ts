import { Injectable, signal } from '@angular/core';

/**
 * Servizio per la gestione dello stato del breadcrumb nell'applicazione.
 */
@Injectable({
  providedIn: 'root',
})
export class BreadcrumbService {
  private _breadcrumbData = signal<string | undefined>(undefined);
  public readonly breadcrumbData = this._breadcrumbData.asReadonly();

  constructor() {}

  /**
   * Imposta il dato del breadcrumb nello stato locale.
   * @param data Stringa breadcrumb da impostare
   * @returns void
   */
  setBreadcrumbData(data: string | undefined): void {
    this._breadcrumbData.set(data);
  }
}
