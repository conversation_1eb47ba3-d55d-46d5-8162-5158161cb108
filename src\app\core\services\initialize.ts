/** Core */
import { Location } from '@angular/common';
import { inject, Injectable } from '@angular/core';
import { GenericUtils } from '@core/utils/generic';
import { environment } from '@env/environment';
import {
  catchError,
  combineLatest,
  from,
  map,
  Observable,
  of,
  tap,
} from 'rxjs';
import { AuthService } from './http/auth';
import { CategoriesService } from './http/categories';
import { CollectionsService } from './http/collections';
import { SocketService } from './socket';
import { LanguageService } from './utils/language';
import { ThemeService } from './utils/theme';

const ROUTES_NO_LOGOUT = ['/auth/activate-account', '/auth/reset-password'];

@Injectable({ providedIn: 'root' })
export class InitializeService {
  // SERVICES
  private authService = inject(AuthService);
  private location = inject(Location);
  private socketService = inject(SocketService);
  private languageService = inject(LanguageService);
  private themeService = inject(ThemeService);
  private categoriesService = inject(CategoriesService);
  private collectionsService = inject(CollectionsService);

  initThemeAndLanguage(): Observable<any>[] {
    this.languageService.configureLang(environment.languages);
    return [
      from(this.themeService.loadTheme()),
      from(this.languageService.loadLanguage()),
    ];
  }

  private initUser(): Observable<any> {
    const token = localStorage.getItem(GenericUtils.session_token);
    const adminId = localStorage.getItem(GenericUtils.user_id);

    if (!!token && !!adminId) {
      // GET STAFF PROFILE
      return this.authService.getStaffProfile(adminId).pipe(
        tap(() => {
          this.authService.setLoggedIn(true);
          this.socketService.initSocket();
        }),
        map(() => true),
        catchError(() => {
          return this.authService.logout().pipe(map(() => true));
        }),
      );
    } else {
      const currentUrl = this.location.path();

      if (ROUTES_NO_LOGOUT.find((item) => currentUrl.startsWith(item))) {
        return of(true);
      } else {
        return this.authService.logout().pipe(map(() => true));
      }
    }
  }

  private initCategories(): Observable<any> {
    return this.categoriesService.readAll();
  }

  private initCollections(): Observable<any> {
    return this.collectionsService.readAll();
  }

  initConfigApp(): Observable<any> {
    // Execute config and theme/language initialization in parallel
    return combineLatest([
      this.initUser(),
      ...this.initThemeAndLanguage(),
      this.initCategories(),
      // this.initCollections(),
    ]);
  }
}
