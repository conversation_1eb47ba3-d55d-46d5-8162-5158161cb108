<div class="container">
  <nz-spin [nzSpinning]="loading">
    <form nz-form [formGroup]="baseForm">
      <div nz-row>
        <div nz-col nzSpan="8" class="w-100 pr-8">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'name'"
            [label]="'STAFF.name'"
            [placeholder]="'INPUTS.namePlaceholder'"
            [minLength]="2"
            [maxLength]="50"
          ></app-input-generic>
        </div>
        <div nz-col nzSpan="8" class="w-100">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'surname'"
            [label]="'STAFF.surname'"
            [placeholder]="'INPUTS.surnamePlaceholder'"
            [minLength]="2"
            [maxLength]="50"
          ></app-input-generic>
        </div>
      </div>
      <div nz-row>
        <div nz-col nzSpan="8" class="w-100 pr-8">
          <app-input-generic
            [parentForm]="baseForm"
            [controlName]="'email'"
            [label]="'STAFF.email'"
            [placeholder]="'INPUTS.emailPlaceholder'"
            [minLength]="5"
            [maxLength]="50"
          ></app-input-generic>
        </div>
        @if (crudMode == crudActionType.update) {
          <div nz-col nzSpan="8" class="w-100">
            <div nz-row class="w-100 tags" style="justify-content: flex-end">
              <div style="margin-right: 1.3rem">
                <app-tag-active-status
                  [value]="baseForm.get('isActive').value"
                ></app-tag-active-status>
              </div>
              <div>
                <app-tag-enable-status
                  [value]="baseForm.get('isEnabled').value"
                ></app-tag-enable-status>
              </div>
            </div>
          </div>
        }
      </div>
    </form>
  </nz-spin>
</div>

<ng-template #tplButton>
  <app-simple-button
    [disabled]="!baseForm.valid"
    (onButtonClick)="onDataSaveClick()"
    [title]="buttonTitle | translate"
    [icon]="'save'"
  ></app-simple-button>
</ng-template>
